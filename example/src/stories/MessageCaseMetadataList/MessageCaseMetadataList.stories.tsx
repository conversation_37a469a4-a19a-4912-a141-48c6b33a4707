import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { CustomText, MessageCaseMetadataList } from 'b-ui-lib';

const CASE_METADATA = [
  {
    id: '1',
    name: '3rd Parties',
    description:
      'NAVIOS SHIP MANAGEMENT META INC(L1_32) NAVIOS SHIP MANAGEMENT META INC(L1_32)',
  },
  {
    id: '2',
    name: '3rd Parties',
    description: 'B',
  },
  {
    id: '3',
    name: '3rd Parties',
    description: 'Some value',
  },
];

const meta = {
  title: 'MessageCaseMetadataList',
  component: MessageCaseMetadataList,
  argTypes: {
    caseMetadata: { control: 'object', description: 'Case metadata list' },
  },
  args: {
    caseMetadata: CASE_METADATA,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 10,
          }}
        >
          <CustomText>Metadata</CustomText>
          <CustomText>2</CustomText>
        </View>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageCaseMetadataList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
