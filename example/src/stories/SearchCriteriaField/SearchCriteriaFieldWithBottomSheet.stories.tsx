import { useRef, useState } from 'react';
import { type TextStyle, View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import {
  CustomBottomSheet,
  CustomText,
  SearchCriteriaField,
  type SearchCriteriaFieldProps,
} from 'b-ui-lib';
import type BottomSheet from '@gorhom/bottom-sheet';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';

const meta = {
  title: 'SearchCriteriaFieldWithBottomSheet',
  component: SearchCriteriaField,
  argTypes: {
    value: { control: 'text', description: 'Input value' },
    onChangeText: {
      action: 'Change input text',
      description: 'Handler for when the input changes',
    },
    label: { control: 'text', description: 'Input label' },
    placeholder: { control: 'text', description: 'Input placeholder' },
    editable: { control: 'boolean', description: 'If input is editable' },
    iconName: { control: 'text', description: 'Icon name' },
    iconColor: { control: 'text', description: 'Icon color' },
    onPress: {
      action: 'On input press',
      description: 'On input press',
    },
    suggestions: { control: 'object', description: 'Suggestions' },
    handleSuggestionPress: {
      action: 'Handle suggestion press',
      description: 'Handle suggestion press',
    },
    style: { control: 'object', description: 'Styles' },
    testID: { control: 'text', description: 'testID' },
  },
  args: {
    value: '',
    onChangeText: () => {},
    label: 'Some Field',
    placeholder: 'Company Prefix',
    editable: true,
    iconName: 'plus',
    iconColor: '',
    onPress: () => {},
    suggestions: [],
    handleSuggestionPress: () => {},
    style: undefined,
    testID: '',
  },
  decorators: [
    (Story) => (
      <View style={{ flex: 1 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof SearchCriteriaField>;

export default meta;

type Story = StoryObj<typeof meta>;

const WithValueState = (props: SearchCriteriaFieldProps) => {
  const [inputValue, setInputValue] = useState<string>('');
  const bottomSheetRef = useRef<BottomSheet>(null);

  const handleSheetChanges = (index: number) => {
    console.log({ index });
  };

  const expandBottomSheet = () => {
    bottomSheetRef?.current?.expand();
  };

  const handlePressBottomSheetOption = (value: string) => {
    bottomSheetRef?.current?.close();
    setInputValue(value);
  };

  const bottomSheetActionStyle: TextStyle = {
    padding: 10,
    borderWidth: 1,
    fontWeight: 'bold',
  };

  return (
    <GestureHandlerRootView>
      <NavigationContainer>
        <SearchCriteriaField
          {...props}
          onPress={expandBottomSheet}
          editable={false}
          value={inputValue}
          style={{ container: { margin: 20 } }}
        />

        <CustomBottomSheet
          bottomSheetRef={bottomSheetRef}
          handleSheetChanges={handleSheetChanges}
          hasBackdrop
        >
          <View
            style={{
              flexDirection: 'row',
              gap: 20,
              justifyContent: 'center',
              padding: 5,
            }}
          >
            <CustomText
              style={bottomSheetActionStyle}
              onPress={() => handlePressBottomSheetOption('1')}
            >
              1
            </CustomText>
            <CustomText
              style={bottomSheetActionStyle}
              onPress={() => handlePressBottomSheetOption('2')}
            >
              2
            </CustomText>
            <CustomText
              style={bottomSheetActionStyle}
              onPress={() => handlePressBottomSheetOption('3')}
            >
              3
            </CustomText>
          </View>
        </CustomBottomSheet>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
};

export const Basic: Story = {
  render: (args) => <WithValueState {...args} />,
};
