import { useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { SearchCriteriaField, type SearchCriteriaFieldProps } from 'b-ui-lib';

const meta = {
  title: 'SearchCriteriaField',
  component: SearchCriteriaField,
  argTypes: {
    value: { control: 'text', description: 'Input value' },
    onChangeText: {
      action: 'Change input text',
      description: 'Handler for when the input changes',
    },
    label: { control: 'text', description: 'Input label' },
    placeholder: { control: 'text', description: 'Input placeholder' },
    editable: { control: 'boolean', description: 'If input is editable' },
    iconName: { control: 'text', description: 'Icon name' },
    iconColor: { control: 'text', description: 'Icon color' },
    onPress: {
      action: 'On input press',
      description: 'On input press',
    },
    suggestions: { control: 'object', description: 'Suggestions' },
    handleSuggestionPress: {
      action: 'Handle suggestion press',
      description: 'Handle suggestion press',
    },
    errorText: { control: 'text', description: 'Error text' },
    style: { control: 'object', description: 'Styles' },
    testID: { control: 'text', description: 'testID' },
  },
  args: {
    value: '',
    onChangeText: () => {},
    label: 'Some Field',
    placeholder: 'Company Prefix',
    editable: true,
    iconName: 'plus',
    iconColor: '',
    onPress: () => {},
    suggestions: [],
    handleSuggestionPress: () => {},
    errorText: '',
    style: undefined,
    testID: '',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, flex: 1 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof SearchCriteriaField>;

export default meta;

type Story = StoryObj<typeof meta>;

const WithValueState = (props: SearchCriteriaFieldProps) => {
  const [inputValue, setInputValue] = useState<string>('');

  const handleSetValue = (textValue: string) => setInputValue(textValue);

  return (
    <SearchCriteriaField
      {...props}
      value={inputValue}
      onChangeText={handleSetValue}
    />
  );
};

export const Basic: Story = {
  render: (args) => <WithValueState {...args} />,
};

export const BasicWithErrorText: Story = {
  render: (args) => <WithValueState {...args} errorText="Some Error Text" />,
};
