import { View } from 'react-native';
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MessageReplyList } from 'b-ui-lib';

const REPLIES_DATA = [
  {
    id: '1',
    avatarName: 'AG',
    username: 'Userna<PERSON>',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '2',
    avatarName: 'M<PERSON>',
    username: '<PERSON>rna<PERSON>',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '3',
    avatarName: 'S',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '4',
    avatarName: 'AG',
    username: 'Userna<PERSON>',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '5',
    avatarName: 'M<PERSON>',
    username: 'Userna<PERSON>',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '6',
    avatarName: 'S',
    username: '<PERSON>rna<PERSON>',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '7',
    avatarName: 'AG',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '8',
    avatarName: 'MJ',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '9',
    avatarName: 'S',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '10',
    avatarName: 'AG',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '11',
    avatarName: 'MJ',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
  {
    id: '12',
    avatarName: 'S',
    username: 'Username',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
  },
];

const meta = {
  title: 'ReplyList',
  component: MessageReplyList,
  argTypes: {
    replies: { control: 'object', description: 'Replies object' },
  },
  args: {
    replies: REPLIES_DATA,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageReplyList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
