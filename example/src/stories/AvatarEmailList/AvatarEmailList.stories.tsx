import { useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { type AvatarEmailListProps, AvatarEmailList } from 'b-ui-lib';

const EMAILS_LIST = [
  {
    id: '1',
    value: '<EMAIL>',
    recipient: 'Username#1',
    avatarName: 'N',
    avatarBackgroundColor: '#FFB7CD',
  },
  {
    id: '2',
    value: '<EMAIL>',
    recipient: '',
    avatarName: 'K',
    avatarBackgroundColor: '#FF8833',
  },
  {
    id: '3',
    value: '<EMAIL>',
    recipient: 'Username#3',
    avatarName: 'A',
    avatarBackgroundColor: '#FFB7CD',
  },
  {
    id: '4',
    value: '<EMAIL>',
    recipient: 'Username#4',
    avatarName: 'A',
    avatarBackgroundColor: '#FF8833',
  },
  {
    id: '5',
    value: '<EMAIL>',
    recipient: 'Username#5',
    avatarName: 'P',
    avatarBackgroundColor: '#FF8833',
  },
  {
    id: '6',
    value: '',
    recipient: 'Username#6',
    avatarName: 'L',
    avatarBackgroundColor: '#FFB7CD',
  },
];

const meta = {
  title: 'AvatarEmailList',
  component: AvatarEmailList,
  argTypes: {
    emails: { control: 'object', description: 'Emails list' },
    selectedEmailId: { control: 'text', description: 'Selected email id' },
    handleSelectEmail: {
      action: 'Select email',
      description: 'Select email',
    },
  },
  args: {
    emails: EMAILS_LIST,
    selectedEmailId: '',
    handleSelectEmail: () => {},
  },
  decorators: [
    (Story) => (
      <View>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof AvatarEmailList>;

export default meta;

type Story = StoryObj<typeof meta>;

const AvatarEmailListWithHooks = (props: AvatarEmailListProps) => {
  const [selectedEmailId, setSelectedEmailId] = useState<string>('');

  const handleSelectEmail = (emailId: string) => setSelectedEmailId(emailId);

  return (
    <AvatarEmailList
      {...props}
      emails={EMAILS_LIST}
      selectedEmailId={selectedEmailId}
      handleSelectEmail={handleSelectEmail}
    />
  );
};

export const Basic: Story = {
  render: (args) => <AvatarEmailListWithHooks {...args} />,
};
