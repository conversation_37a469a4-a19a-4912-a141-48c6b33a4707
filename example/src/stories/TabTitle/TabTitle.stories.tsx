import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { TabTitle } from 'b-ui-lib';

const meta = {
  title: 'TabTitle',
  component: TabTitle,
  argTypes: {
    title: { control: 'text', description: 'Title text' },
    count: { control: 'text', description: 'Optional count display' },
    containerStyles: {
      control: 'object',
      description: 'Custom styles for the container',
    },
  },
  args: {
    title: 'Messages',
    count: '10',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof TabTitle>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
