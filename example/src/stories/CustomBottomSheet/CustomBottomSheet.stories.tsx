import { useRef, useState } from 'react';
import { type TextStyle, View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import type BottomSheet from '@gorhom/bottom-sheet';
import {
  Button,
  CustomBottomSheet,
  type CustomBottomSheetProps,
  CustomText,
} from 'b-ui-lib';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { NavigationContainer } from '@react-navigation/native';

const meta = {
  title: 'Custom BottomSheet',
  component: CustomBottomSheet,
  argTypes: {
    bottomSheetRef: { control: 'object', description: 'Bottom Sheet ref' },
    handleSheetChanges: {
      action: 'Tap to select email',
      description: 'Tap to select email',
    },
    handleBackPress: {
      action: 'Tap to select email',
      description: 'Tap to select email',
    },
    backPressDependencies: {
      control: 'object',
      description: 'BackPress dependencies',
    },
    snapPoints: {
      control: 'object',
      description: 'Bottom sheet snap points',
    },
    hasBackdrop: {
      control: 'boolean',
      description: 'Has backdrop',
    },
    handleBackdropPress: {
      action: 'Press backdrop',
      description: 'Press backdrop',
    },
    style: {
      control: 'object',
      description: 'Styles',
    },
    children: {
      control: 'object',
      description: 'Children',
    },
  },
  args: {
    bottomSheetRef: null,
    handleSheetChanges: () => {},
    handleBackPress: () => false,
    backPressDependencies: [],
    snapPoints: [],
    hasBackdrop: false,
    handleBackdropPress: () => {},
    style: null,
    children: null,
  },
  decorators: [
    (Story) => (
      <View
        style={{
          flex: 1,
          backgroundColor: '#f2f2f3',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof CustomBottomSheet>;

export default meta;

type Story = StoryObj<typeof meta>;

const CustomBottomSheetWithHooks = (props: CustomBottomSheetProps) => {
  const [isBottomSheetExpanded, setIsBottomSheetExpanded] =
    useState<boolean>(false);
  const bottomSheetRef = useRef<BottomSheet>(null);

  const handleSheetChanges = (index: number) => {
    console.log({ index });
  };

  const handleBackPress = () => {
    if (isBottomSheetExpanded) {
      bottomSheetRef?.current?.close();
      setIsBottomSheetExpanded(false);

      return true;
    }

    return false;
  };

  const expandBottomSheet = () => {
    bottomSheetRef?.current?.expand();
    setIsBottomSheetExpanded(true);
  };

  const bottomSheetActionStyle: TextStyle = {
    padding: 10,
    borderWidth: 1,
    fontWeight: 'bold',
  };

  return (
    <GestureHandlerRootView>
      <NavigationContainer>
        <Button
          containerStyle={{ margin: 20 }}
          title="Open bottom sheet"
          onPress={expandBottomSheet}
        />

        <CustomBottomSheet
          {...props}
          bottomSheetRef={bottomSheetRef}
          handleSheetChanges={handleSheetChanges}
          handleBackPress={handleBackPress}
          hasBackdrop
        >
          <View
            style={{
              flexDirection: 'row',
              gap: 20,
              justifyContent: 'center',
              padding: 5,
            }}
          >
            <CustomText style={bottomSheetActionStyle}>1</CustomText>
            <CustomText style={bottomSheetActionStyle}>2</CustomText>
            <CustomText style={bottomSheetActionStyle}>3</CustomText>
          </View>
        </CustomBottomSheet>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
};

export const Primary: Story = {
  args: {
    bottomSheetRef: null,
    handleSheetChanges: () => {},
    handleBackPress: () => false,
    backPressDependencies: [],
    snapPoints: [],
    style: null,
  },
  render: (args) => <CustomBottomSheetWithHooks {...args} />,
};
