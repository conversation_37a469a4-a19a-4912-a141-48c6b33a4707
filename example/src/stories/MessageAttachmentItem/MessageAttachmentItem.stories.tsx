import type { Meta, StoryObj } from '@storybook/react';
import { AttachmentItem } from 'b-ui-lib';
import { View } from 'react-native';

const ATTACHMENT = {
  id: '1',
  name: 'Rating.xml',
};

const meta = {
  title: 'MessageAttachmentItem',
  component: AttachmentItem,
  argTypes: {
    attachment: { control: 'object', description: 'Attachment' },
  },
  args: {
    attachment: ATTACHMENT,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof AttachmentItem>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
