import { StyleSheet, View } from 'react-native';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MessageCommentItem } from 'b-ui-lib';

const COMMENT = {
  id: '1',
  avatarName: 'S',
  username: 'Username1',
  date: '2024-12-26T10:15:30.000Z',
  description:
    "<p><table style=' background-color:#F2E8DA; border:1px dotted gray; width:100%; clear:both; font-size:12px; margin-top:5px; margin-bottom:10px; border-spacing:0px;'><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Message Id:</b> [BNFTSTG-12494]</td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Sent:</b> Tuesday, Dec 10, 2024 13:30 (UTC +02:00)</td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>From:</b> <EMAIL></td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>To:</b> <EMAIL></td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Subject:</b> Display name 2</td></tr></table> </p>\n" +
    '<p style="font-family:Verdana,Arial,sans-serif; font-size:12px; margin-bottom:20px; margin-left:0; margin-right:0; margin-top:0; padding:0">Admin</p>\n',
  isStarred: true,
  attachmentIds: ['1', '2'],
  attachments: [],
  notifiedUsers: ['<EMAIL>', '<EMAIL>'],
  replies: [],
};

const meta = {
  title: 'MessageCommentItem',
  component: MessageCommentItem,
  argTypes: {
    comment: { control: 'object', description: 'MessageComment object' },
    isExpanded: {
      control: 'boolean',
      description: 'Are comment replies expanded',
    },
    handlePressExpand: {
      action: 'Handle press expand',
      description: 'Handle press expand',
    },
    handlePressStar: {
      action: 'Press the star icon',
      description: 'Press the star icon',
    },
    handlePressReply: {
      action: 'Handle press messageReplyList',
      description: 'Handle press messageReplyList',
    },
    attachmentsListModalTitle: {
      control: 'text',
      description: 'Attachments list modal title',
    },
    recipientsListModalTitle: {
      control: 'text',
      description: 'Recipients list modal title',
    },
    handleDownloadAttachment: {
      action: 'Handle download attachments',
      description: 'Handle download attachments',
    },
    containerStyle: { control: 'object', description: 'Container styles' },
  },
  args: {
    comment: COMMENT,
    isExpanded: false,
    handlePressExpand: () => {},
    handlePressStar: () => {},
    handlePressReply: () => {},
    isNested: false,
    attachmentsListModalTitle: '',
    recipientsListModalTitle: '',
    handleDownloadAttachment: () => {},
    handleDownloadAllAttachments: () => {},
    attachments: { byId: {}, allIds: [] },
    containerStyle: {},
    fetchNotifiedUsers: () => {},
    fetchNotifiedUsersLoading: false,
    fetchNotifiedUsersError: null,
    participantsList: [],
  },
  decorators: [
    (Story) => (
      <View style={styles.container}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageCommentItem>;

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
});

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    comment: COMMENT,
    isExpanded: false,
    handlePressExpand: () => {},
    handlePressStar: () => {},
    handlePressReply: () => {},
    attachmentsListModalTitle: '',
    recipientsListModalTitle: '',
    handleDownloadAttachment: () => {},
    containerStyle: {},
  },
};
