import { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import {
  type CommentRecipientsListModalProps,
  IconTextButton,
  ICON_POSITIONS,
  CommentRecipientsListModal,
} from 'b-ui-lib';

const EMAILS = [
  {
    id: '1',
    value: 'andreasg<PERSON><EMAIL>',
    recipient: 'recipient1',
    avatarName: 'A',
    avatarBackgroundColor: '',
  },
  {
    id: '2',
    value: '<EMAIL>',
    recipient: 'recipient2',
    avatarName: 'B',
    avatarBackgroundColor: '',
  },
];

const meta = {
  title: 'CommentRecipientsListModal',
  component: CommentRecipientsListModal,
  argTypes: {
    isVisible: { control: 'boolean', description: 'If modal is visible' },
    handleClose: {
      action: '<PERSON>le close the modal',
      description: '<PERSON>le close the modal',
    },
    title: { control: 'text', description: 'Title' },
    notifiedUsers: {
      control: 'object',
      description: 'Emails',
    },
  },
  args: {
    isVisible: false,
    handleClose: () => {},
    title: '',
    notifiedUsers: [],
    fetchNotifiedUsersError: null,
    fetchNotifiedUsersLoading: false,
  },
  decorators: [
    (Story) => (
      <View style={styles.container}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof CommentRecipientsListModal>;

export default meta;

type Story = StoryObj<typeof meta>;

const CommentRecipientsListModalWithHooks = (
  props: CommentRecipientsListModalProps
) => {
  const [isVisible, setIsVisible] = useState<boolean>(false);

  const handleOpen = () => setIsVisible(true);
  const handleClose = () => {
    setIsVisible(false);
  };

  return (
    <View style={styles.modalItems}>
      <IconTextButton
        iconPosition={ICON_POSITIONS.right}
        iconName="user"
        iconSize={16}
        title="2"
        onPress={handleOpen}
        textStyle={styles.textStyle}
      />

      <CommentRecipientsListModal
        {...props}
        isVisible={isVisible}
        handleClose={handleClose}
        title=""
        notifiedUsers={EMAILS}
        fetchNotifiedUsersError={null}
        fetchNotifiedUsersLoading={false}
      />
    </View>
  );
};

export const Primary: Story = {
  render: (args) => <CommentRecipientsListModalWithHooks {...args} />,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f2f2f3',
  },
  modalItems: {
    alignItems: 'center',
  },
  textStyle: {
    fontSize: 12,
    color: 'black',
  },
});
