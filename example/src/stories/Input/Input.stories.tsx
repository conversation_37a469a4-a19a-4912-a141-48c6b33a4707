import { useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { Input, type InputProps } from 'b-ui-lib';

const meta = {
  title: 'Input',
  component: Input,
  argTypes: {
    value: { control: 'text', description: 'Input value' },
    onChangeText: {
      action: 'Change input text',
      description: 'Handler for when the input changes',
    },
    label: { control: 'text', description: 'Input label' },
    placeholder: { control: 'text', description: 'Input placeholder' },
    isPassword: { control: 'boolean', description: 'Is input password' },
    error: { control: 'text', description: 'Input error' },
    showSoftInputOnFocus: {
      control: 'boolean',
      description: 'it will prevent the soft keyboard from showing',
    },
    multiline: { control: 'boolean', description: 'Is input multiline' },
    numberOfLines: { control: 'number', description: 'Input number of lines' },
  },
  args: {
    value: '',
    onChangeText: () => {},
    label: 'Company Prefix',
    placeholder: 'Some placeholder',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, flex: 1 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof Input>;

export default meta;

type Story = StoryObj<typeof meta>;

const WithValueState = (props: InputProps) => {
  const [inputValue, setInputValue] = useState<string>('');

  const handleSetValue = (textValue: string) => setInputValue(textValue);

  return <Input {...props} value={inputValue} onChangeText={handleSetValue} />;
};

export const Basic: Story = {
  render: (args) => <WithValueState {...args} />,
};
