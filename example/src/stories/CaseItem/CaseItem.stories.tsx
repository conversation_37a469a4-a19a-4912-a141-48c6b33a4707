import type { Meta, StoryObj } from '@storybook/react';
import { CaseItem, DISPLAY_MODE } from 'b-ui-lib';

const meta: Meta<typeof CaseItem> = {
  title: 'CaseItem',
  component: CaseItem,
  argTypes: {
    title: { control: 'text' },
    date: { control: 'date' },
    casReference: { control: 'text' },
    assignedTo: { control: 'text' },
    from: { control: 'text' },
    containerStyle: { control: 'object', description: 'Styles' },
    onPress: {
      action: 'Tap case',
      description: 'Tap case',
    },
    displayMode: {
      control: 'text',
      description: 'List item display mode',
    },
  },
  args: {
    title: 'Navios // LMS Requests and Certificates',
    date: '2021-09-01',
    casReference: 'CAS-1662',
    assignedTo: '#username#',
    from: '#username1#',
    containerStyle: {},
    onPress: () => {},
    displayMode: DISPLAY_MODE.linkedCase,
  },
  decorators: [(Story) => <Story />],
} satisfies Meta<typeof CaseItem>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
