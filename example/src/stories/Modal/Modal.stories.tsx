import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import {
  Button,
  BUTTON_DEFAULT_VARIANTS,
  Modal,
  type ModalProps,
} from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'Modal',
  component: Modal,
  argTypes: {
    isVisible: { control: 'boolean', description: 'If modal is visible' },
    isCloseIconVisible: {
      control: 'boolean',
      description: 'If modal is visible',
    },
    handleClose: {
      action: 'Handle close the modal',
      description: '<PERSON>le close the modal',
    },
    title: { control: 'text', description: 'Title' },
    subtitle: { control: 'text', description: 'Subtitle' },
    children: {
      control: 'object',
      description: 'Component children',
    },
    buttonsArray: {
      control: 'object',
      description: 'Buttons array',
    },
    componentStyles: {
      control: 'object',
      description: 'Component object styles',
    },
  },
  args: {
    isVisible: false,
    isCloseIconVisible: false,
    title: 'Notification',
    subtitle: 'Are you sure you want to delete the current email?',
    handleClose: () => {},
    children: null,
    buttonsArray: [],
    componentStyles: {},
  },
  decorators: [
    (Story) => (
      <View
        style={{
          flex: 1,
          padding: 20,
          backgroundColor: '#f2f2f3',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof Modal>;

export default meta;

type Story = StoryObj<typeof meta>;

const ModalWithHooks = (props: ModalProps) => {
  const [isVisible, setIsVisible] = useState<boolean>(false);

  const handleOpen = () => setIsVisible(true);
  const handleClose = () => {
    setIsVisible(false);
  };

  const BUTTONS_ARRAY = [
    {
      title: 'Delete',
      onPress: () => {},
      variant: BUTTON_DEFAULT_VARIANTS.primary,
    },
    {
      title: 'Cancel',
      onPress: () => handleClose(),
      variant: BUTTON_DEFAULT_VARIANTS.secondary,
    },
  ];

  return (
    <View style={{ alignItems: 'center' }}>
      <Button title="Open Modal" onPress={handleOpen} />
      <Modal
        {...props}
        isVisible={isVisible}
        handleClose={handleClose}
        buttonsArray={BUTTONS_ARRAY}
      />
    </View>
  );
};

export const Primary: Story = {
  render: (args) => <ModalWithHooks {...args} />,
};
