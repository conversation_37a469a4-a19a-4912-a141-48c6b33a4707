import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { type CheckboxProps, Checkbox, CustomText } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'Checkbox',
  component: Checkbox,
  argTypes: {
    disableText: { control: 'boolean', description: 'Disable text' },
    size: { control: 'number', description: 'Size' },
    isChecked: { control: 'boolean', description: 'is checked' },
    onPress: {
      action: 'Handle checkbox press',
      description: 'Handle checkbox press',
    },
    hitSlop: { control: 'object', description: 'Touchable hitSlop' },
    style: { control: 'object', description: 'Style' },
    innerIconStyle: { control: 'object', description: 'InnerIconStyle' },
    iconStyle: { control: 'object', description: 'IconStyle' },
    fillColor: { control: 'text', description: 'Fill color' },
    unFillColor: { control: 'text', description: 'Un fill color' },
    testID: { control: 'text', description: 'testID' },
  },
  args: {
    disableText: false,
    size: 20,
    isChecked: false,
    onPress: () => {},
    hitSlop: undefined,
    style: {},
    innerIconStyle: {},
    iconStyle: {},
    fillColor: '',
    unFillColor: '',
    testID: '',
  },
  decorators: [
    (Story) => (
      <View
        style={{
          flex: 1,
          backgroundColor: '#f2f2f3',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof Checkbox>;

export default meta;

type Story = StoryObj<typeof meta>;

const CheckboxWithHooks = (props: CheckboxProps) => {
  const [isChecked, setIsChecked] = useState<boolean>(false);

  const toggleChecked = () => setIsChecked(!isChecked);

  return (
    <View
      style={{
        flexDirection: 'row',
        backgroundColor: '#a0a0a3',
        padding: 20,
      }}
    >
      <Checkbox {...props} isChecked={isChecked} onPress={toggleChecked} />
      <CustomText>{isChecked ? 'Checked' : 'UnChecked'}</CustomText>
    </View>
  );
};

export const Primary: Story = {
  render: (args) => <CheckboxWithHooks {...args} />,
};
