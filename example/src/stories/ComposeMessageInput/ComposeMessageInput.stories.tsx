import { useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { type ComposeMessageInputProps, ComposeMessageInput } from 'b-ui-lib';

const meta = {
  title: 'ComposeMessageInput',
  component: ComposeMessageInput,
  argTypes: {
    selectedEmail: { control: 'text', description: 'Selected email' },
    isBottomSheetOpen: {
      control: 'boolean',
      description: 'Is the bottom sheet open',
    },
    handleArrowPress: {
      action: 'Press the arrow icon',
      description: 'Press the arrow icon',
    },
    containerStyle: { control: 'object', description: 'Container styles' },
  },
  args: {
    selectedEmail: '',
    isBottomSheetOpen: false,
    handleArrowPress: () => {},
    containerStyle: {},
    title: 'Compose Message',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof ComposeMessageInput>;

export default meta;

type Story = StoryObj<typeof meta>;

const ComposeMessageInputWithHooks = (props: ComposeMessageInputProps) => {
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState<boolean>(false);

  const handleArrowPress = () => setIsBottomSheetOpen(!isBottomSheetOpen);

  return (
    <ComposeMessageInput
      {...props}
      selectedEmail="<EMAIL>"
      isBottomSheetOpen={isBottomSheetOpen}
      handleArrowPress={handleArrowPress}
    />
  );
};

export const Primary: Story = {
  args: {
    selectedEmail: '',
    isBottomSheetOpen: false,
    handleArrowPress: () => {},
    containerStyle: {},
  },
  render: (args) => <ComposeMessageInputWithHooks {...args} />,
};
