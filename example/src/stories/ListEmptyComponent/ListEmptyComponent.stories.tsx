import type { Meta, StoryObj } from '@storybook/react';
import { ListEmptyComponent } from 'b-ui-lib';
import { FlatList, View } from 'react-native';

const meta = {
  title: 'ListEmptyComponent',
  component: ListEmptyComponent,
  argTypes: {
    emptyText: { control: 'text', description: 'Empty text to show' },
    style: { control: 'object', description: 'Styles' },
  },
  args: {
    emptyText: '',
    style: {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <FlatList
          data={[]}
          renderItem={({ item }) => item}
          ListEmptyComponent={<Story />}
        />
      </View>
    ),
  ],
} satisfies Meta<typeof ListEmptyComponent>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
