import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button, BUTTON_DEFAULT_VARIANTS } from 'b-ui-lib';
import { Alert, View } from 'react-native';

const meta = {
  title: 'Button',
  component: Button,
  argTypes: {
    variant: {
      control: { type: 'radio' },
      description: 'Button variants',
      options: [
        BUTTON_DEFAULT_VARIANTS.primary,
        BUTTON_DEFAULT_VARIANTS.secondary,
        BUTTON_DEFAULT_VARIANTS.secondaryWithGrayBackground,
      ],
    },
    title: { control: 'text', description: 'Title' },
    onPress: {
      action: 'pressed the button',
      description: 'Handler for when the button is pressed',
    },
    isLoading: { control: 'boolean', description: 'Loading' },
    loaderColor: { control: 'text', description: 'Loader color' },
    isDisabled: { control: 'boolean', description: 'isDisabled' },
    testID: { control: 'text', description: 'testID' },
  },
  args: {
    variant: BUTTON_DEFAULT_VARIANTS.primary,
    title: 'Delete',
    isLoading: false,
    onPress: () => Alert.alert('Pressed'),
    testID: '',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof Button>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Primary: Story = {};
