import { StyleSheet, View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import {
  type Theme,
  type LogoProps,
  Logo,
  useThemeAwareObject,
} from 'b-ui-lib';

const meta = {
  title: 'Logo',
  component: Logo,
  argTypes: {
    source: { control: 'text', description: 'Logo width' },
    width: { control: 'number', description: 'Logo width' },
    height: { control: 'number', description: 'Logo height' },
  },
  args: {
    source: undefined,
    width: undefined,
    height: undefined,
  },
  decorators: [
    (Story) => (
      <View>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof Logo>;

export default meta;

type Story = StoryObj<typeof meta>;

const LogoWithHooks = (props: LogoProps) => {
  const { images } = useThemeAwareObject((theme) => createStyles(theme));

  return <Logo {...props} source={images.B_SIGNATURE_LOGO} />;
};

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({});

  return { styles, color, images };
};

export const Basic: Story = {
  render: (args) => <LogoWithHooks {...args} />,
};
