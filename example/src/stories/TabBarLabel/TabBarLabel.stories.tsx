import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { TabBarLabel } from 'b-ui-lib';

const meta = {
  title: 'TabBarLabel',
  component: TabBarLabel,
  argTypes: {
    iconName: { control: 'text', description: 'Name of the icon' },
    focused: {
      control: 'boolean',
      description: 'Indicates if the tab is focused',
    },
    count: { control: 'text', description: 'Optional count to display' },
    onPress: { action: 'pressed', description: 'Callback when pressed' },
    testID: { control: 'text', description: 'TestID' },
  },
  args: {
    iconName: 'home',
    focused: true,
    count: '5',
    testID: '',
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof TabBarLabel>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    iconName: 'home',
    focused: true,
    count: '5',
    onPress: () => {},
  },
};
