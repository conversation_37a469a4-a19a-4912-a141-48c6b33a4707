import type { <PERSON>a, StoryObj } from '@storybook/react';
import { View } from 'react-native';
import { MessageListTab } from 'b-ui-lib';

const MESSAGES = [
  {
    id: 'e9ee1a43-eab6-ef11-bdfe-6045bd2aaf50',
    messageKey: '25',
    type: '1',
    inOut: 1,
    attachmentsCount: 5,
    from: '"Google admin test" <<EMAIL>>',
    to: '<EMAIL>',
    cc: '',
    subject: 'Display name 2',
    avatarName: 'AD',
    sentDate: '2024-12-10T11:31:14.62',
    username: 'Admin',
  },
];

const meta = {
  title: 'MessageListTab',
  component: MessageListTab,
  argTypes: {
    messages: { control: 'object', description: 'Messages List' },
    tabTitle: { control: 'text', description: 'Tab title' },
    handleTapMessage: {
      action: 'Tap message',
      description: 'Tap message',
    },
    handleRefreshList: {
      action: 'Refresh list',
      description: 'Refresh list',
    },
    isLoading: { control: 'boolean', description: 'Is loading' },
    errorMessage: { control: 'text', description: 'Error message' },
    containerStyle: { control: 'object', description: 'Styles' },
  },
  args: {
    messages: MESSAGES,
    tabTitle: '',
    handleTapMessage: () => {},
    handleRefreshList: () => {},
    isLoading: false,
    errorMessage: '',
    containerStyle: {},
  },
  decorators: [
    (Story) => (
      <View style={{ flex: 1 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageListTab>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
