import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { CustomText, MessageCaseList } from 'b-ui-lib';

const CASES = [
  {
    id: '1',
    name: 'CAS-1662 Navios // LMS Requests and Certificates',
    date: '2024-12-26T10:15:30.000Z',
  },
  {
    id: '2',
    name: 'CAS-1662 Navios // LMS Requests and Certificates',
    date: '2024-12-26T10:15:30.000Z',
  },
  {
    id: '3',
    name: 'CAS-1662 Navios // LMS Requests and Certificates',
    date: '2024-12-26T10:15:30.000Z',
  },
];

const meta = {
  title: 'MessageCaseList',
  component: MessageCaseList,
  argTypes: {
    cases: { control: 'object', description: 'Case list' },
  },
  args: {
    cases: CASES,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 10,
          }}
        >
          <CustomText>Cases</CustomText>
          <CustomText>2</CustomText>
        </View>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageCaseList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
