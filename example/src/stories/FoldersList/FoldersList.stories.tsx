import type { <PERSON>a, StoryObj } from '@storybook/react';
import { type Folder, type FolderListProps, FolderList } from 'b-ui-lib';
import { View } from 'react-native';
import { useState } from 'react';

const FOLDERS = [
  {
    id: '6d5e92b9-fef3-4dd5-bca8-1b1b9170f5eb',
    name: 'All',
    isExpanded: true,
    isSearchItem: false,
    emailsCount: 10,
    children: [
      {
        id: '56572e16-b8a3-43b4-9174-bb112d4f6a30',
        name: 'Incoming',
        isExpanded: true,
        isSearchItem: false,
        emailsCount: 0,
        parentId: '6d5e92b9-fef3-4dd5-bca8-1b1b9170f5eb',
        children: [
          {
            id: '56572e16-b8a3-43b4-9174-bb112d4f6a31',
            name: 'Some search term',
            isExpanded: true,
            isSearchItem: true,
            emailsCount: 0,
            parentId: '56572e16-b8a3-43b4-9174-bb112d4f6a30',
            children: [],
          },
        ],
      },
    ],
  },
  {
    id: '67ff4292-025e-4c4a-a6d1-de1284ff3b85',
    name: 'Sent',
    isExpanded: true,
    isSearchItem: false,
    emailsCount: 0,
    children: [
      {
        id: 'ef068215-ddd6-4d2b-99d5-3efeff7f5858',
        name: 'Drafts',
        isExpanded: true,
        isSearchItem: false,
        emailsCount: 0,
        parentId: '67ff4292-025e-4c4a-a6d1-de1284ff3b85',
        children: [],
      },
    ],
  },
  {
    id: '54cfea7d-11a6-4032-8b2f-bbf7778ad96e',
    name: 'Junk',
    isExpanded: true,
    isSearchItem: false,
    emailsCount: 0,
    children: [],
  },
  {
    id: 'b5750d03-927c-4697-b95f-07778ad008db',
    name: 'Deleted Items',
    isExpanded: true,
    isSearchItem: false,
    emailsCount: 0,
    children: [],
  },
  {
    id: '50ea71d6-7501-e611-8bbe-8eeb09241d8e',
    name: 'Frequently Used Folders',
    isExpanded: true,
    isSearchItem: false,
    emailsCount: 0,
    children: [],
  },
  {
    id: '3c76e6bc-4912-49f7-97ed-5636ba17cd0e',
    name: 'Folders',
    isExpanded: true,
    emailsCount: 0,
    children: [],
  },
  {
    id: '3e488911-ff88-4925-911c-99d50af9902b',
    name: 'admin',
    isExpanded: true,
    emailsCount: 0,
    children: [],
  },
];

const meta = {
  title: 'Folder List',
  component: FolderList,
  argTypes: {
    folders: { control: 'object', description: 'Folders' },
    selectedFolderId: { control: 'text', description: 'Selected folder id' },
    handleFolderPress: {
      action: 'Handle folder press',
      description: 'Handle folder press',
    },
    handleFolderPressSearchItem: {
      action: 'Handle folder press search item',
      description: 'Handle folder press search item',
    },
    handleFolderExpand: {
      action: 'Handle folder expand',
      description: 'Handle folder expand',
    },
    emptyFoldersTitle: { control: 'text', description: 'Empty folders title' },
  },
  args: {
    folders: FOLDERS,
    selectedFolderId: '',
    handleFolderPress: () => {},
    handleFolderExpand: () => {},
  },
  decorators: [
    (Story) => (
      <View>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof FolderList>;

export default meta;

type Story = StoryObj<typeof meta>;

const FolderListWithHooks = (props: FolderListProps) => {
  const [folders, setFolders] = useState<Folder[]>(FOLDERS);
  const [selectedFolderId, setSelectedFolderId] = useState<string>('');

  const toggleFolderExpansion = (
    allFolders: Folder[],
    folderId?: string
  ): any[] => {
    return allFolders.map((folder) => {
      // If this folder matches the id, toggle its isExpanded value
      if (folder.id === folderId) {
        return { ...folder, isExpanded: !folder.isExpanded };
      }
      // If the folder has children, recursively toggle expansion for each child
      if (folder.children && folder.children.length > 0) {
        return {
          ...folder,
          children: toggleFolderExpansion(folder.children, folderId),
        };
      }
      return folder;
    });
  };

  const handleFolderPress = (folderId: string) => {
    if (folderId === selectedFolderId) {
      return setSelectedFolderId('');
    }

    setSelectedFolderId(folderId);
  };

  const handleFolderExpand = (folderId: string) => {
    setFolders(toggleFolderExpansion(folders, folderId));
  };

  return (
    <FolderList
      {...props}
      folders={folders}
      selectedFolderId={selectedFolderId}
      handleFolderPress={handleFolderPress}
      handleFolderExpand={handleFolderExpand}
    />
  );
};

export const Primary: Story = {
  render: (args) => <FolderListWithHooks {...args} />,
};
