import type { Meta, StoryObj } from '@storybook/react';
import { Button, TextWithDuration, type TextWithDurationProps } from 'b-ui-lib';
import { View } from 'react-native';
import { useState } from 'react';

const meta = {
  title: 'TextWithDuration',
  component: TextWithDuration,
  argTypes: {
    text: { control: 'text', description: 'Text' },
    isSuccess: { control: 'boolean', description: 'isSuccess' },
    isError: { control: 'boolean', description: 'isError' },
    style: { control: 'object', description: 'Style' },
    duration: { control: 'number', description: 'Duration' },
  },
  args: {
    text: 'Please try again later',
  },
  decorators: [
    (Story) => (
      <View
        style={{
          marginHorizontal: 120,
          alignItems: 'center',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof TextWithDuration>;

export default meta;

type Story = StoryObj<typeof meta>;

const TextWithDurationWithHooks = (props: TextWithDurationProps) => {
  const [errorMessage, setErrorMessage] = useState<string>('');

  const addErrorMessage = () => setErrorMessage('Some Error Message');

  const clearErrorMessage = () => setErrorMessage('');

  return (
    <View style={{ justifyContent: 'center', marginVertical: 50, gap: 30 }}>
      <Button title="Add Error Message" onPress={addErrorMessage} />

      <TextWithDuration
        {...props}
        text={errorMessage}
        cbWhenComponentHide={clearErrorMessage}
        style={{ color: 'red' }}
      />
    </View>
  );
};

export const Basic: Story = {
  render: (args) => <TextWithDurationWithHooks {...args} />,
};
