import type { Meta, StoryObj } from '@storybook/react';
import { AttachmentList } from 'b-ui-lib';
import { View } from 'react-native';

const ATTACHMENTS = [
  {
    id: '1',
    name: 'Rating.xml',
    isLoading: false,
  },
  {
    id: '2',
    name: '23441234_checklist.xml',
    isLoading: true,
  },
];

const meta = {
  title: 'AttachmentList',
  component: AttachmentList,
  argTypes: {
    attachments: { control: 'object', description: 'List of attachments' },
    handleDownload: {
      action: 'Download attachmentList',
      description: 'Download attachmentList',
    },
  },
  args: {
    attachments: ATTACHMENTS,
    handleDownload: () => {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof AttachmentList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
