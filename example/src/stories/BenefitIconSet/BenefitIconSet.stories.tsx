import type { Meta, StoryObj } from '@storybook/react';
import { BenefitIconSet } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'BenefitIconSet',
  component: BenefitIconSet,
  argTypes: {
    name: { control: 'text', description: "<PERSON><PERSON>'s name" },
    size: { control: 'number', description: 'Size' },
  },
  args: {
    name: 'arrow-down',
    size: 24,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof BenefitIconSet>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
