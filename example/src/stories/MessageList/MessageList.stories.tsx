import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { MessageList, type MessagesSectionListProps } from 'b-ui-lib';
import { Alert, View } from 'react-native';
import { useState } from 'react';

const SECTIONS = [
  {
    title: '03 Dec 2024',
    data: [
      {
        id: '1a2b3c4d-1234-abcd-5678-ef1234567890',
        messageKey: '001',
        sentDate: '2024-12-03T10:15:00',
        username: 'Admin',
        from: '2alice@example.com1111111111',
        to: '<EMAIL>',
        subject: 'Meeting Reminder: Budget Review',
        body: 'Hi <PERSON>, Just a reminder about tomorrow’s meeting to review the budget. Please make sure to bring the updated figures. Thanks, <PERSON>.',
        avatarName: 'a',
        isFlagged: true,
        attachmentsCount: 3,
        hasReplies: true,
        hasAttachments: true,
        hasComments: true,
        hasMetadata: true,
        hasFolders: true,
        hasCases: true,
        inOut: 1,
        type: 'Email',
        isViewed: false,
      },
    ],
  },
  {
    title: '02 Dec 2024',
    data: [
      {
        id: '4d5e6f7g-4567-def0-8901-234567890123',
        messageKey: '004',
        sentDate: '2024-12-02T10:15:00',
        username: 'Admin',
        from: '2alice@example.com111111',
        to: '<EMAIL>',
        subject: 'Urgent: Policy Update Effective Immediately',
        body: 'Dear Team, Please review the attached document for important updates to our workplace policies. Let us know if you have any questions.',
        avatarName: 'h',
        isFlagged: true,
        attachmentsCount: 2,
        hasReplies: true,
        hasAttachments: true,
        hasComments: false,
        hasMetadata: true,
        hasFolders: true,
        hasCases: false,
        inOut: 1,
        type: 'Email',
        isViewed: true,
      },
      {
        id: '5e6f7g8h-5678-ef01-9012-345678901234',
        messageKey: '005',
        sentDate: '2024-12-02T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Lunch Plans?',
        body: 'Hi Mary, Are you free for lunch today? Let me know what works for you.',
        avatarName: 'j',
        isFlagged: false,
        attachmentsCount: 0,
        hasReplies: true,
        hasAttachments: false,
        hasComments: true,
        hasMetadata: true,
        hasFolders: true,
        hasCases: false,
        inOut: 1,
        type: 'Email',
        isViewed: false,
      },
      {
        id: '7g8h9i0j-7890-0123-1234-567890123456',
        messageKey: '007',
        sentDate: '2024-12-02T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Project Deadline Reminder',
        body: 'Hi Team, This is a reminder that the final deadline for the project is December 5th. Please ensure all deliverables are submitted on time. Let’s finish strong!',
        avatarName: 'p',
        isFlagged: true,
        attachmentsCount: 4,
        hasReplies: true,
        hasAttachments: true,
        hasComments: true,
        hasMetadata: true,
        hasFolders: true,
        hasCases: true,
        inOut: 1,
        type: 'Email',
        isViewed: false,
      },
    ],
  },
  {
    title: '01 Dec 2024',
    data: [
      {
        id: '2b3c4d5e-2345-bcde-6789-f01234567891',
        messageKey: '002',
        sentDate: '2024-12-01T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Your Support Request (#12345)',
        body: 'Hello Jane, Thank you for reaching out. We are reviewing your request and will get back to you within 24 hours. Regards, Support Team.',
        avatarName: 's',
        isFlagged: false,
        attachmentsCount: 0,
        hasReplies: false,
        hasAttachments: false,
        hasComments: false,
        hasMetadata: true,
        hasFolders: false,
        hasCases: true,
        inOut: 2,
        type: 'Email',
        isViewed: true,
      },
      {
        id: '3c4d5e6f-3456-cdef-7890-123456789012',
        messageKey: '003',
        sentDate: '2024-12-01T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'December Newsletter: Top Stories',
        body: 'Hello Subscriber, This month, we’re sharing the top stories in technology, health, and lifestyle. Don’t miss out!',
        avatarName: 'n',
        isFlagged: false,
        attachmentsCount: 1,
        hasReplies: false,
        hasAttachments: false,
        hasComments: false,
        hasMetadata: false,
        hasFolders: false,
        hasCases: false,
        inOut: 2,
        type: 'Email',
        isViewed: true,
      },
      {
        id: '4c4d5e6f-3456-cdef-7890-123456789012',
        messageKey: '003',
        sentDate: '2024-12-01T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'December Newsletter: Top Stories',
        body: 'Hello Subscriber, This month, we’re sharing the top stories in technology, health, and lifestyle. Don’t miss out!',
        avatarName: 'n',
        isFlagged: false,
        attachmentsCount: 1,
        hasReplies: true,
        hasAttachments: true,
        hasComments: true,
        hasMetadata: true,
        hasFolders: true,
        hasCases: true,
        inOut: 2,
        type: 'Email',
        isViewed: true,
      },
      {
        id: '5c4d5e6f-3456-cdef-7890-123456789012',
        messageKey: '003',
        sentDate: '2024-12-01T10:15:00',
        username: 'Admin',
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'December Newsletter: Top Stories',
        body: 'Hello Subscriber, This month, we’re sharing the top stories in technology, health, and lifestyle. Don’t miss out!',
        avatarName: 'n',
        isFlagged: false,
        attachmentsCount: 1,
        hasReplies: true,
        hasAttachments: true,
        hasComments: false,
        hasMetadata: true,
        hasFolders: false,
        hasCases: false,
        inOut: 2,
        type: 'Email',
        isViewed: true,
      },
    ],
  },
];

const meta = {
  title: 'Message List',
  component: MessageList,
  argTypes: {
    sections: { control: 'object', description: 'Sections' },
    emptyText: {
      control: 'text',
      description: 'Empty message when empty list',
    },
    isSkeletonLoading: {
      control: 'boolean',
      description: 'Is skeleton loading',
    },
    isMultiSelectActive: {
      control: 'boolean',
      description: 'If the multiselect of section list is active',
    },
    selectedMessagesIds: {
      control: 'object',
      description: 'Ids of selected messages',
    },
    handleTapMessage: {
      action: 'Tap to select email',
      description: 'Tap to select email',
    },
    handleLongTapToSelectEmail: {
      action: 'Long tap to select email',
      description: 'Long tap to select email',
    },
    handleTapToSelectAdditionalEmail: {
      action: 'Tap to select email',
      description: 'Tap to select email',
    },
    handleDeselectMessage: {
      action: 'Deselect email',
      description: 'Deselect email',
    },
    handleFlagPress: {
      action: 'Handle flag pressed',
      description: "Message's flag icon pressed",
    },
  },
  args: {
    initialNumToRender: 10,
    loadMoreEmails: () => {},
    listFooterComponent: undefined,
    sections: [],
    emptyText: '',
    isSkeletonLoading: false,
    isMultiSelectActive: false,
    selectedMessagesIds: [],
    handleTapMessage: () => {},
    handleLongTapToSelectEmail: () => {},
    handleTapToSelectAdditionalEmail: () => {},
    handleDeselectMessage: () => {},
    handleFlagPress: () => {},
  },
  decorators: [
    (Story) => (
      <View
        style={{
          flex: 1,
          backgroundColor: '#f2f2f3',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageList>;

export default meta;
type Story = StoryObj<typeof meta>;

const MessagesSectionListWithHooks = (props: MessagesSectionListProps) => {
  const [isMultiSelectActive, setIsMultiSelectActive] =
    useState<boolean>(false);
  const [selectedMessagesIds, setSelectedMessagesIds] = useState<string[]>([]);

  const handleLongTapToSelectEmail = (messageId: string) => {
    setSelectedMessagesIds([...selectedMessagesIds, messageId]);
    setIsMultiSelectActive(true);
  };

  const handleTapToSelectAdditionalEmail = (messageId: string) => {
    setSelectedMessagesIds([...selectedMessagesIds, messageId]);
  };

  const handleDeselectMessage = (messageId: string) => {
    const newSelectedMessagesIds = selectedMessagesIds.filter(
      (id) => id !== messageId
    );

    setSelectedMessagesIds(newSelectedMessagesIds);
  };

  const handleFlagPress = () => {
    Alert.alert('handleFlagPress');
  };

  return (
    <MessageList
      {...props}
      sections={SECTIONS}
      emptyText=""
      isSkeletonLoading={false}
      isMultiSelectActive={isMultiSelectActive}
      selectedMessagesIds={selectedMessagesIds}
      handleTapMessage={() => {}}
      handleLongTapToSelectEmail={handleLongTapToSelectEmail}
      handleTapToSelectAdditionalEmail={handleTapToSelectAdditionalEmail}
      handleDeselectMessage={handleDeselectMessage}
      handleFlagPress={handleFlagPress}
    />
  );
};

export const Primary: Story = {
  args: {
    sections: SECTIONS,
    emptyText: '',
    isSkeletonLoading: false,
    isMultiSelectActive: false,
    selectedMessagesIds: [],
    handleTapMessage: () => {},
    handleLongTapToSelectEmail: () => {},
    handleTapToSelectAdditionalEmail: () => {},
    handleDeselectMessage: () => {},
    handleFlagPress: () => {},
  },
  render: (args) => <MessagesSectionListWithHooks {...args} />,
};
