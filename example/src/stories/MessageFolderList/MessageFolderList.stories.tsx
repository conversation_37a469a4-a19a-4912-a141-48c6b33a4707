import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { MessageFolderList } from 'b-ui-lib';

const FOLDERS = [
  {
    id: '1',
    name: 'inbox',
    path: 'All / Shared Folders / Vessel Name / Shared /',
    backgroundColor: '',
  },
  {
    id: '2',
    name: 'NAVIOS',
    path: 'All / Shared Folders / Vessel Name / Shared /',
    backgroundColor: '',
  },
  {
    id: '3',
    name: 'NAVIOS 2',
    path: 'All / Shared Folders / Vessel Name / Shared /',
    backgroundColor: '',
  },
];

const meta = {
  title: 'MessageFolderList',
  component: MessageFolderList,
  argTypes: {
    folders: { control: 'object', description: 'Comments list' },
  },
  args: {
    folders: FOLDERS,
  },
  decorators: [
    (Story) => (
      <View style={{}}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageFolderList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
