import type { <PERSON>a, StoryObj } from '@storybook/react';
import { View } from 'react-native';
import { CaseList } from 'b-ui-lib';

const CASES = [
  {
    CAS_Guid: 'e9ee1a43-eab6-ef11-bdfe-6045bd2aaf50',
    CAS_Title: 'Navios // LMS Requests and Certificates',
    CAS_Reference: 'CAS-1662',
    USR_Name_AssignedTo: '#username#',
    CAS_CreatedTimestamp: '2021-09-01',
    CreatedUserName: '#username1#',
  },
];

const meta = {
  title: 'CaseList',
  component: CaseList,
  argTypes: {
    cases: { control: 'object', description: 'Cases' },
    handleTapCase: {
      action: 'Tap case',
      description: 'Tap case',
    },
    handleRefreshList: {
      action: 'Refresh list',
      description: 'Refresh list',
    },
    isLoading: { control: 'boolean', description: 'Is loading' },
    errorMessage: { control: 'text', description: 'Error message' },
    loadMore: {
      action: 'Load more case',
      description: 'Load more case',
    },
    listFooterComponent: {
      control: 'object',
      description: 'List footer component',
    },
    displayMode: {
      control: 'text',
      description: 'List item display mode',
    },
    containerStyle: { control: 'object', description: 'Styles' },
  },
  args: {
    cases: CASES,
    handleTapCase: () => {},
    handleRefreshList: () => {},
    isLoading: false,
    errorMessage: '',
    loadMore: () => {},
    listFooterComponent: null,
    displayMode: '',
    containerStyle: {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof CaseList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
