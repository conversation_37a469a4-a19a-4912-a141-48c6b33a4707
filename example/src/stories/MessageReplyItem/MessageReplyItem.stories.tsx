import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { MessageReplyItem } from 'b-ui-lib';

const meta = {
  title: 'ReplyItem',
  component: MessageReplyItem,
  argTypes: {
    avatarName: { control: 'text', description: 'Avatar name' },
    username: { control: 'text', description: 'Username text' },
    from: { control: 'text', description: 'From text' },
    date: { control: 'text', description: 'Date text' },
    containerStyle: { control: 'object', description: 'Container styles' },
  },
  args: {
    avatarName: 'AG',
    username: '<PERSON><PERSON>',
    from: '<EMAIL>',
    date: 'Sep12, 12:23',
    containerStyle: {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageReplyItem>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
