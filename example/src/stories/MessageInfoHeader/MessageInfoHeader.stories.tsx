import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { MessageInfoHeader } from 'b-ui-lib';

const meta = {
  title: 'MessageInfoHeader',
  component: MessageInfoHeader,
  argTypes: {
    subject: { control: 'text', description: 'Subject' },
    sentDate: { control: 'text', description: 'Send date' },
    notificationIcon: { username: 'text', description: 'Notification icon' },
    containerStyle: { username: 'object', description: 'Container style' },
  },
  args: {
    subject: 'Some text',
    sentDate: '2025-02-19T09:59:15',
    notificationIcon: 'mail',
    containerStyle: {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageInfoHeader>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
