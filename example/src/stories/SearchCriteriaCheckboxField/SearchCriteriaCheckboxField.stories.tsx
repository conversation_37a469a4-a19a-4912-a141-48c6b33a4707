import { useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import {
  type CheckboxOption,
  SearchCriteriaCheckboxField,
  type SearchCriteriaCheckboxFieldProps,
} from 'b-ui-lib';

const CHECKBOX_OPTIONS = [
  {
    name: 'option1',
    value: 'option1',
  },
  {
    name: 'option2',
    value: 'option2',
  },
  {
    name: 'option3',
    value: 'option3',
  },
];

const meta = {
  title: 'SearchCriteriaCheckboxField',
  component: SearchCriteriaCheckboxField,
  argTypes: {
    value: { control: 'text', description: 'Input value' },
    label: { control: 'text', description: 'Input label' },
    handleCheckboxPress: {
      action: 'Handle checkbox press',
      description: 'Handle checkbox press',
    },
    checkboxOptions: { control: 'object', description: 'Checkbox options' },
  },
  args: {
    value: '',
    label: 'Search In',
    handleCheckboxPress: () => {},
    checkboxOptions: CHECKBOX_OPTIONS,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, flex: 1 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof SearchCriteriaCheckboxField>;

export default meta;

type Story = StoryObj<typeof meta>;

const WithValueState = (props: SearchCriteriaCheckboxFieldProps) => {
  const [inputValue, setInputValue] = useState<string>('');

  const toggleString = (base: string, value: string) => {
    let items = base ? base.trim().split(',') : [];

    if (items.includes(value)) {
      items = items.filter((item) => item !== value);
    } else {
      items.push(value);
    }

    return items.join(',');
  };

  const handleCheckboxPress = (checkboxOption: CheckboxOption) =>
    setInputValue(toggleString(inputValue, checkboxOption.value));

  return (
    <SearchCriteriaCheckboxField
      {...props}
      value={inputValue}
      handleCheckboxPress={handleCheckboxPress}
    />
  );
};

export const Basic: Story = {
  render: (args) => <WithValueState {...args} />,
};
