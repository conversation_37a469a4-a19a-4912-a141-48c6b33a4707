import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { MessageInfoData } from 'b-ui-lib';

const meta = {
  title: 'MessageInfoData',
  component: MessageInfoData,
  argTypes: {
    isHidden: { control: 'boolean', description: 'Is component hidden' },
    isSkeletonLoading: {
      control: 'boolean',
      description: 'Is skeleton loading',
    },
    skeletonBackgroundColor: {
      control: 'text',
      description: 'Skeleton background color',
    },
    avatarName: { control: 'text', description: 'Avatar name' },
    inOut: { control: 'number', description: 'type of email' },
    from: { control: 'text', description: 'From' },
    tos: { control: 'text', description: 'Tos' },
    ccs: { control: 'text', description: 'Ccs' },
    bccs: { control: 'text', description: 'Bccs' },
    containerStyle: { control: 'object', description: 'Container style' },
  },
  args: {
    isHidden: false,
    isSkeletonLoading: false,
    avatarName: 'AN',
    inOut: 1,
    from: '<EMAIL>',
    tos: '<EMAIL>',
    ccs: '<EMAIL>',
    bccs: '<EMAIL>, <EMAIL>',
    containerStyle: {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageInfoData>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
