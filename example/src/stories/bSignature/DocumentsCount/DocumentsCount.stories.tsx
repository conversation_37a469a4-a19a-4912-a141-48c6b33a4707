import type { Meta, StoryObj } from '@storybook/react';
import { DocumentsCount } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'DocumentsCount',
  component: DocumentsCount,
  argTypes: {
    count: { control: 'text', description: 'Count number to show' },
    text: { control: 'text', description: 'Text to show' },
    countColor: { control: 'text', description: 'Count color' },
    style: { control: 'object', description: 'styles' },
  },
  args: {
    count: '5',
    text: 'Completed',
    countColor: 'orange',
    style: {},
  },
  decorators: [
    (Story) => (
      <View style={{ flexDirection: 'row', gap: 5, padding: 20 }}>
        <Story />
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof DocumentsCount>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
