import type { Meta, StoryObj } from '@storybook/react';
import { StatusLabel } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'StatusLabel',
  component: StatusLabel,
  argTypes: {
    label: { control: 'text', description: 'Status label' },
    backgroundColor: {
      control: 'text',
      description: 'Status backgroundColor',
    },
    style: { control: 'object', description: 'Styles' },
  },
  args: {
    label: 'Activated',
    backgroundColor: '#88CC29',
    style: { container: {} },
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof StatusLabel>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
