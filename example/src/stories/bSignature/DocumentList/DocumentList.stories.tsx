import { useMemo, useState } from 'react';
import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import {
  type DocumentItem,
  DocumentList,
  type DocumentListProps,
  SearchInput,
} from 'b-ui-lib';

const MOCK_DOCUMENTS: DocumentItem[] = [
  {
    id: '1',
    name: 'Contract_2025_01.pdf',
    statusDescription: 'Activated',
    statusBackgroundColor: '#4CAF50',
    signedDate: '2025-04-01T12:00:00.000Z',
  },
  {
    id: '2',
    name: 'Policy_Review_2025_02.pdf',
    statusDescription: 'Pending',
    statusBackgroundColor: '#FFC107',
    signedDate: '2025-04-01T12:00:00.000Z',
  },
  {
    id: '3',
    name: 'Agreement_Final_2025_03.pdf',
    statusDescription: 'Completed',
    statusBackgroundColor: '#2196F3',
    signedDate: '2025-04-01T12:00:00.000Z',
  },
];

const meta = {
  title: 'DocumentList',
  component: DocumentList,
  argTypes: {
    documents: { control: 'object', description: 'Documents' },
    handlePressDocument: {
      action: 'Handle press document',
      description: 'Handle press document',
    },
    isLoading: { control: 'boolean', description: 'Is loading' },
    errorMessage: { control: 'text', description: 'Error message' },
    handleRefreshList: {
      action: 'Handle refresh list',
      description: 'Handle refresh list',
    },
    listEmptyMessage: { control: 'text', description: 'List empty message' },
    skeletonItemCount: {
      control: 'number',
      description: 'Skeleton items count',
    },
    hasDateField: {
      control: 'boolean',
      description: 'If the document item show the date field',
    },
  },
  args: {
    documents: [],
    handlePressDocument: () => {},
    isLoading: false,
    errorMessage: '',
    handleRefreshList: () => {},
    listEmptyMessage: '',
    skeletonItemCount: 5,
    hasDateField: false,
  },
  decorators: [
    (Story) => (
      <View
        style={{
          flex: 1,
          padding: 20,
          backgroundColor: '#f2f2f3',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof DocumentList>;

export default meta;

type Story = StoryObj<typeof meta>;

const DocumentListWithHooks = (props: DocumentListProps) => {
  const [searchInputValue, setSearchInputValue] = useState<string>('');

  const documents = useMemo(() => {
    if (!searchInputValue) {
      return MOCK_DOCUMENTS;
    }

    return MOCK_DOCUMENTS.filter((document) =>
      document.name.includes(searchInputValue)
    );
  }, [searchInputValue]);

  const handleSetSearchInputValue = (inputValue: string) =>
    setSearchInputValue(inputValue);
  const handleInputClear = () => setSearchInputValue('');

  return (
    <View style={{ gap: 20 }}>
      <SearchInput
        placeholder="Search for a document"
        value={searchInputValue}
        onChangeText={handleSetSearchInputValue}
        handleInputClear={handleInputClear}
      />
      <DocumentList {...props} documents={documents} />
    </View>
  );
};

export const Primary: Story = {
  args: {},
  render: (args) => <DocumentListWithHooks {...args} />,
};
