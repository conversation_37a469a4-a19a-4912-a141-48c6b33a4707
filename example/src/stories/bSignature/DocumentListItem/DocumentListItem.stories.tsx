import type { Meta, StoryObj } from '@storybook/react';
import { DocumentListItem } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'DocumentListItem',
  component: DocumentListItem,
  argTypes: {
    title: { control: 'text', description: 'Document title' },
    date: { control: 'text', description: 'Document date' },
    statusDescription: {
      control: 'text',
      description: 'Document status description',
    },
    statusBackgroundColor: {
      control: 'text',
      description: 'Document status backgroundColor',
    },
    onPress: {
      action: 'Handle document press',
      description: 'Handle document press',
    },
    hideArrowIcon: {
      control: 'boolean',
      description: 'Hide arrow icon',
    },
  },
  args: {
    title: 'Some Document',
    date: '2025-04-03T18:35:12.123Z',
    statusDescription: 'Activated',
    statusBackgroundColor: 'orange',
    onPress: () => {},
    hideArrowIcon: false,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof DocumentListItem>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
