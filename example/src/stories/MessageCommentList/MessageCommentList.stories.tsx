import { View } from 'react-native';
import type { Meta, StoryObj } from '@storybook/react';
import { MessageCommentList } from 'b-ui-lib';

const COMMENTS = [
  {
    id: '1',
    avatarName: 'S',
    username: 'Username1',
    date: '2024-12-26T10:15:30.000Z',
    description:
      "<p><table style=' background-color:#F2E8DA; border:1px dotted gray; width:100%; clear:both; font-size:12px; margin-top:5px; margin-bottom:10px; border-spacing:0px;'><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Message Id:</b> [BNFTSTG-12494]</td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Sent:</b> Tuesday, Dec 10, 2024 13:30 (UTC +02:00)</td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>From:</b> <EMAIL></td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>To:</b> <EMAIL></td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Subject:</b> Display name 2</td></tr></table> </p>\n" +
      '<p style="font-family:Verdana,Arial,sans-serif; font-size:12px; margin-bottom:20px; margin-left:0; margin-right:0; margin-top:0; padding:0">Admin</p>\n',
    isStarred: true,
    usersCount: 2,
    attachmentIds: ['1', '2'],
    attachments: [
      {
        id: '1',
        name: 'attachments1',
        isLoading: false,
      },
      {
        id: '2',
        name: 'attachments2',
        isLoading: false,
      },
    ],
    notifiedUsers: ['1', '2'],
    replies: [],
  },
  {
    id: '2',
    avatarName: 'S',
    username: 'Username2',
    date: '2024-12-26T10:15:30.000Z',
    description:
      "<p><table style=' background-color:#F2E8DA; border:1px dotted gray; width:100%; clear:both; font-size:12px; margin-top:5px; margin-bottom:10px; border-spacing:0px;'><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Message Id:</b> [BNFTSTG-12494]</td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Sent:</b> Tuesday, Dec 10, 2024 13:30 (UTC +02:00)</td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>From:</b> <EMAIL></td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>To:</b> <EMAIL></td></tr><tr style='color:black; margin:0 0 0px 3px; width:100%; clear:both;'><td><b>Subject:</b> Display name 2</td></tr></table> </p>\n" +
      '<p style="font-family:Verdana,Arial,sans-serif; font-size:12px; margin-bottom:20px; margin-left:0; margin-right:0; margin-top:0; padding:0">Admin</p>\n',
    isStarred: true,
    usersCount: 2,
    attachmentIds: ['1', '2'],
    attachments: [
      {
        id: '1',
        name: 'attachments1',
        isLoading: false,
      },
      {
        id: '2',
        name: 'attachments2',
        isLoading: false,
      },
    ],
    notifiedUsers: ['1', '2'],
    replies: [],
  },
];

const ATTACHMENTS = {
  byId: {
    1: [
      {
        id: '1',
        name: 'attachments1',
        isLoading: false,
      },
    ],
    2: [
      {
        id: '2',
        name: 'attachments2',
        isLoading: false,
      },
    ],
  },
  allIds: ['1', '2'],
};

const meta = {
  title: 'MessageCommentList',
  component: MessageCommentList,
  argTypes: {
    comments: { control: 'object', description: 'Comments object' },
    handlePressStar: {
      action: 'Press the star icon',
      description: 'Press the star icon',
    },
    handlePressReply: {
      action: 'Handle press messageReplyList',
      description: 'Handle press messageReplyList',
    },
    attachmentsListModalTitle: {
      control: 'text',
      description: 'Attachments list modal title',
    },
    recipientsListModalTitle: {
      control: 'text',
      description: 'Recipients list modal title',
    },
    handleDownloadAttachment: {
      action: 'Handle download attachments',
      description: 'Handle download attachments',
    },
    participantsList: {
      control: 'object',
      description: 'Participants list',
    },
  },
  args: {
    comments: COMMENTS,
    handlePressStar: () => {},
    handlePressReply: () => {},
    attachmentsListModalTitle: '',
    recipientsListModalTitle: '',
    handleDownloadAttachment: () => {},
    handleDownloadAllAttachments: () => {},
    attachments: ATTACHMENTS,
    participantsList: [],
    fetchNotifiedUsers: () => {},
    fetchNotifiedUsersLoading: false,
    fetchNotifiedUsersError: null,
  },
  decorators: [
    (Story) => (
      <View>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof MessageCommentList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
