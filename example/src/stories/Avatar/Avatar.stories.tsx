import type { Meta, StoryObj } from '@storybook/react';
import { Avatar } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'Avatar',
  component: Avatar,
  argTypes: {
    name: { control: 'text', description: 'Text to show' },
    backgroundColor: { control: 'color', description: 'Background Color' },
    isSmall: { control: 'boolean', description: 'Is small avatar' },
    style: { control: 'object', description: 'Styles' },
  },
  args: {
    name: 'A',
    backgroundColor: 'purple',
    isSmall: false,
    style: {},
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof Avatar>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
