import type { Meta, StoryObj } from '@storybook/react';
import { CustomText } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'CustomText',
  component: CustomText,
  argTypes: {
    numberOfLines: { control: 'number', description: 'Number of lines' },
    canCopy: { control: 'boolean', description: 'Copy text with long press' },
  },
  args: {
    numberOfLines: 0,
  },
  decorators: [
    (Story) => (
      <View
        style={{
          marginHorizontal: 120,
          alignItems: 'center',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof CustomText>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Primary: Story = {};
