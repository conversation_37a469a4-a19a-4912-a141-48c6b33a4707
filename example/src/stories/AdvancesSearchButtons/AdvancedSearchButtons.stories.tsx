import type { Meta, StoryObj } from '@storybook/react';
import { AdvancedSearchButtons } from 'b-ui-lib';
import { View } from 'react-native';

const meta = {
  title: 'AdvancedSearchButtons',
  component: AdvancedSearchButtons,
  argTypes: {
    searchFiltersCount: {
      control: 'number',
      description: 'search filters count',
    },
    isClearAllButtonVisible: {
      control: 'boolean',
      description: 'Is clear button visible',
    },
    handleClearAll: {
      action: 'Clear all filters',
      description: 'Clear all filters',
    },
    handlePressCriteriaButton: {
      action: 'Press criteria button',
      description: 'Press criteria button',
    },
    isSearchCriteriaButtonActive: {
      control: 'boolean',
      description: 'Is search criteria button active',
    },
    style: {
      control: 'object',
      description: 'Styles',
    },
  },
  args: {
    searchFiltersCount: 5,
    handleClearAll: () => {},
    handlePressCriteriaButton: () => {},
    isClearAllButtonVisible: true,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20 }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof AdvancedSearchButtons>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
