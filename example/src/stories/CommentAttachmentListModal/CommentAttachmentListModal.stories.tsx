import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import {
  type CommentAttachmentListModalProps,
  CommentAttachmentListModal,
  IconTextButton,
  FONT_SIZES,
} from 'b-ui-lib';
import { View } from 'react-native';

const ATTACHMENTS = [
  {
    id: '1',
    name: 'attachments1',
  },
  {
    id: '2',
    name: 'attachments2',
  },
];

const meta = {
  title: 'CommentAttachmentListModal',
  component: CommentAttachmentListModal,
  argTypes: {
    isVisible: { control: 'boolean', description: 'If modal is visible' },
    handleClose: {
      action: '<PERSON>le close the modal',
      description: '<PERSON>le close the modal',
    },
    title: { control: 'text', description: 'Title' },
    attachments: {
      control: 'object',
      description: 'Attachments',
    },
    handleDownloadAttachment: {
      action: 'Download attachments',
      description: 'Download attachments',
    },
  },
  args: {
    isVisible: false,
    handleClose: () => {},
    title: '',
    attachments: [],
    handleDownloadAttachment: () => {},
    handleDownloadAllAttachments: () => {},
  },
  decorators: [
    (Story) => (
      <View
        style={{
          flex: 1,
          padding: 20,
          backgroundColor: '#f2f2f3',
        }}
      >
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof CommentAttachmentListModal>;

export default meta;

type Story = StoryObj<typeof meta>;

const CommentAttachmentListModalWithHooks = (
  props: CommentAttachmentListModalProps
) => {
  const [isVisible, setIsVisible] = useState<boolean>(false);

  const handleOpen = () => setIsVisible(true);
  const handleClose = () => {
    setIsVisible(false);
  };

  return (
    <View style={{ alignItems: 'center' }}>
      <IconTextButton
        iconName="paperclip"
        iconSize={16}
        title="2"
        onPress={handleOpen}
        containerStyle={{
          justifyContent: 'flex-start',
          marginTop: 12,
        }}
        textStyle={{
          fontSize: FONT_SIZES.TWELVE,
          color: 'black',
        }}
      />
      <CommentAttachmentListModal
        {...props}
        isVisible={isVisible}
        handleClose={handleClose}
        title=""
        attachments={ATTACHMENTS}
        handleDownloadAttachment={() => {}}
      />
    </View>
  );
};

export const Primary: Story = {
  args: {
    isVisible: false,
    handleClose: () => {},
    title: '',
    attachments: [],
    handleDownloadAttachment: () => {},
  },
  render: (args) => <CommentAttachmentListModalWithHooks {...args} />,
};
