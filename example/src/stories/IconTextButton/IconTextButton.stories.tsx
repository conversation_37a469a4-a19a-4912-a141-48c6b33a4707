import type { Meta, StoryObj } from '@storybook/react';
import { ICON_POSITIONS, IconTextButton } from 'b-ui-lib';
import { Alert, View } from 'react-native';

const meta = {
  title: 'IconTextButton',
  component: IconTextButton,
  argTypes: {
    iconPosition: {
      control: 'radio',
      description: 'Icon position',
      options: [
        ICON_POSITIONS.top,
        ICON_POSITIONS.bottom,
        ICON_POSITIONS.left,
        ICON_POSITIONS.right,
      ],
    },
    iconName: { control: 'text', description: 'Icon name' },
    iconSize: { control: 'number', description: 'Icon size' },
    iconColor: { control: 'color', description: 'Icon color' },
    title: { control: 'text', description: 'Button title' },
    onPress: {
      action: 'pressed the button',
      description: 'Handler for when the button is pressed',
    },
    testID: { control: 'text', description: 'TestID' },
    isDisabled: { control: 'boolean', description: 'Is disabled' },
  },
  args: {
    iconPosition: ICON_POSITIONS.left,
    iconName: 'arrow-left',
    title: 'Copy To Folder',
    onPress: () => Alert.alert('Pressed'),
    testID: '',
    isDisabled: false,
  },
  decorators: [
    (Story) => (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <Story />
      </View>
    ),
  ],
} satisfies Meta<typeof IconTextButton>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Basic: Story = {};
