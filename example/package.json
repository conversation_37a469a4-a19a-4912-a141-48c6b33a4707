{"name": "b-ui-lib-example", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "build:android": "react-native build-android --extra-params \"--no-daemon --console=plain -PreactNativeArchitectures=arm64-v8a\"", "build:ios": "react-native build-ios --scheme BUiLibExample --mode Debug --extra-params \"-sdk iphonesimulator CC=clang CPLUSPLUS=clang++ LD=clang LDPLUSPLUS=clang++ GCC_OPTIMIZATION_LEVEL=0 GCC_PRECOMPILE_PREFIX_HEADER=YES ASSETCATALOG_COMPILER_OPTIMIZATION=time DEBUG_INFORMATION_FORMAT=dwarf COMPILER_INDEX_STORE_ENABLE=NO\"", "storybook-generate": "sb-rn-get-stories"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.0", "@react-native-clipboard/clipboard": "1.14.2", "@react-navigation/native": "6.1.6", "@types/lodash": "^4.14.178", "lodash": "^4.17.21", "react": "18.3.1", "react-content-loader": "7.0.2", "react-native": "0.75.4", "react-native-bouncy-checkbox": "4.1.2", "react-native-gesture-handler": "2.20.1", "react-native-modal": "13.0.1", "react-native-reanimated": "3.16.2", "react-native-render-html": "6.3.4", "react-native-svg": "15.9.0", "react-native-toast-message": "2.2.1", "react-native-vector-icons": "9.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@gorhom/bottom-sheet": "5.0.5", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-community/slider": "^4.5.4", "@react-native/babel-preset": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@storybook/addon-ondevice-actions": "^8.3.9", "@storybook/addon-ondevice-controls": "^8.3.9", "@storybook/react-native": "^8.3.9", "babel-loader": "^8.4.1", "react-dom": "^18.3.1", "react-native-builder-bob": "^0.30.2", "react-native-safe-area-context": "^4.11.1", "react-native-svg": "15.9.0"}, "engines": {"node": ">=18"}}