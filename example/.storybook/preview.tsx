import type { Preview } from '@storybook/react';
import { useColorScheme } from 'react-native';
import { ThemeProvider } from '../../src/theme/themeContext';
import { LIGHT_THEME } from '../../src/constants/theme/LightTheme';
import { DARK_THEME } from '../../src/constants/theme/DarkTheme';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
  },
  decorators: [
    (Story) => {
      const colorScheme = useColorScheme();

      return (
        <ThemeProvider
          initial={colorScheme === 'light' ? LIGHT_THEME : DARK_THEME}
        >
          <Story />
        </ThemeProvider>
      );
    },
  ],
};

export default preview;
