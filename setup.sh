#!/bin/bash

# *** First run this command manually to make script executable.
# chmod +x setup.sh

# This script delete and re-install node-modules and start metro

# Step 0: Load nvm and use the correct Node version
echo "Loading nvm and setting Node version..."
export NVM_DIR="$HOME/.nvm"
# Load nvm
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
# Use the correct Node version
nvm use

# Step 1: Delete node_modules from root
echo "Deleting node_modules from root..."
rm -rf node_modules

# Step 2: Delete node_modules from ./example
echo "Deleting node_modules from ./example..."
rm -rf ./example/node_modules

# Step 3: Delete Pods
echo "Deleting Pods"
rm -rf ./example/ios/Pods
rm -rf ./example/ios/Podfile.lock

# Step 4: Install node-modules in root
echo "Installing node-modules in root..."
yarn

# Step 6: Install Pods
echo "Installing Pods..."
cd ./example/ios
pod install

# Step 6: Generate stories
echo "Generate stories..."
cd ../../
yarn example storybook-generate

# Step 7: Starting metro
echo "Starting metro..."
yarn example start -- --reset-cache
