package com.builib

import android.view.View

import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.SimpleViewManager
import com.facebook.react.uimanager.ViewManagerDelegate
import com.facebook.react.viewmanagers.BUiLibViewManagerDelegate
import com.facebook.react.viewmanagers.BUiLibViewManagerInterface

abstract class BUiLibViewManagerSpec<T : View> : SimpleViewManager<T>(), BUiLibViewManagerInterface<T> {
  private val mDelegate: ViewManagerDelegate<T>

  init {
    mDelegate = BUiLibViewManagerDelegate(this)
  }

  override fun getDelegate(): ViewManagerDelegate<T>? {
    return mDelegate
  }
}
