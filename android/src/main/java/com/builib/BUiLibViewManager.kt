package com.builib

import android.graphics.Color
import com.facebook.react.module.annotations.ReactModule
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.uimanager.ThemedReactContext
import com.facebook.react.uimanager.annotations.ReactProp

@ReactModule(name = BUiLibViewManager.NAME)
class BUiLibViewManager :
  BUiLibViewManagerSpec<BUiLibView>() {
  override fun getName(): String {
    return NAME
  }

  public override fun createViewInstance(context: ThemedReactContext): BUiLibView {
    return BUiLibView(context)
  }

  @ReactProp(name = "color")
  override fun setColor(view: BUiLibView?, color: String?) {
    view?.setBackgroundColor(Color.parseColor(color))
  }

  companion object {
    const val NAME = "BUiLibView"
  }
}
