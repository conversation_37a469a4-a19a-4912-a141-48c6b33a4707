// **Feb20, 3:02PM**
export const formatDate = (dateString?: string) => {
  if (!dateString) {
    return null;
  }

  // TODO Below is a check for utc date strings. Maybe we dont need this.
  // Check if the sentDate string ends with "Z" or "z" to determine if it already includes a UTC timezone indicator
  // This ensures new Date(dateString) interprets the time correctly with the proper timezone
  // const hasSpecificCharacter = /[zZ]$/.test(dateString);
  // const dateStringWithTimezone = hasSpecificCharacter
  //   ? dateString
  //   : `${dateString}Z`;

  const date = new Date(dateString);

  // Get month abbreviation
  const month = date.toLocaleString('en-US', { month: 'short' });

  // Get day of the month
  const day = date.getDate();

  // Get hours and minutes in 12-hour format
  const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
  const minutes = date.getMinutes().toString().padStart(2, '0');

  // Determine AM/PM
  const ampm = date.getHours() >= 12 ? 'PM' : 'AM';

  return `${month}${day}, ${hours}:${minutes}${ampm}`;
};

// **15/11/2024**
export const formatDateOnly = (dateString?: string) => {
  if (!dateString) {
    return null;
  }

  return new Date(dateString).toLocaleDateString('en-GB');
};

// **Sep 12, 2024**
export const formatDateWithYear = (dateString: string): string | null => {
  if (!dateString) {
    return null;
  }

  const date = new Date(dateString);

  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  });
};

// **3.20 PM**
export const formatDateToTimeOnly = (dateString: string): string | null => {
  if (!dateString) {
    return null;
  }

  const date = new Date(dateString);

  return date
    .toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
    .replace(':', '.');
};

// **2:36 am, 12 Sep 2024**
export const formatDateWithTime = (dateString?: string) => {
  if (!dateString) {
    return null;
  }

  const date = new Date(dateString);

  const time = date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  const dayMonthYear = date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
  });

  return `${time.toLowerCase()}, ${dayMonthYear}`;
};
