import { type FC } from 'react';
import { type ScrollViewProps, FlatList } from 'react-native';
import type { AvatarEmail } from './avatarEmail';
import { AvatarEmailItem } from './AvatarEmailItem';

export type AvatarEmailListProps = ScrollViewProps & {
  emails: AvatarEmail[];
  selectedEmailId?: string | string[]; // Support single ID or an array of IDs
  handleSelectEmail?: (emailId: string) => void;
};

export const AvatarEmailList: FC<AvatarEmailListProps> = ({
  emails,
  selectedEmailId,
  handleSelectEmail,
  ...restProps
}) => {
  return (
    <FlatList
      {...restProps}
      keyExtractor={(item) => item?.id}
      data={emails}
      renderItem={({ item }) => (
        <AvatarEmailItem
          key={item.id}
          email={item}
          handleSelectEmail={handleSelectEmail}
          isSelected={
            Array.isArray(selectedEmailId) // Check if selectedEmailId is an array
              ? selectedEmailId.includes(item.id) // Check if item.id is in the array
              : item.id === selectedEmailId // Single ID match
          }
        />
      )}
    />
  );
};
