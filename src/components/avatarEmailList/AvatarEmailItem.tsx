import { type FC } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  Avatar,
  BenefitIconSet,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import type { AvatarEmail } from './avatarEmail';

type Props = {
  email: AvatarEmail;
  handleSelectEmail?: (emailId: string) => void;
  isSelected: boolean;
};

export const AvatarEmailItem: FC<Props> = ({
  email,
  handleSelectEmail,
  isSelected,
}) => {
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isSelected)
  );

  return (
    <Pressable
      style={styles.container}
      onPress={() => handleSelectEmail && handleSelectEmail(email.id)}
    >
      <Avatar
        name={email.avatarName}
        backgroundColor={email.avatarBackgroundColor}
      />
      <View>
        {email?.recipient && (
          <CustomText style={styles.emailRecipient}>
            {email?.recipient}
          </CustomText>
        )}
        {email.value && (
          <CustomText style={styles.emailValue}>{email.value}</CustomText>
        )}
      </View>
      {isSelected && (
        <BenefitIconSet
          style={styles.checkIcon}
          name="check"
          size={20}
          color={color.MESSAGE_FLAG}
        />
      )}
    </Pressable>
  );
};

const createStyles = ({ color }: Theme, isSelected: boolean) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: SPACING.M,
      alignItems: 'center',
      paddingVertical: SPACING.EIGHTEEN,
      paddingHorizontal: SPACING.TWENTY_TWO,
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER_EMAIL_CATEGORY,
      backgroundColor: isSelected
        ? color.MESSAGE_ITEM__SELECTED_BACKGROUND
        : color.MESSAGE_ITEM__BACKGROUND,
    },
    emailRecipient: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.MESSAGE_FLAG,
    },
    emailValue: {
      color: color.MESSAGE_FLAG,
    },
    checkIcon: {
      marginLeft: 'auto',
    },
  });

  return { styles, color };
};
