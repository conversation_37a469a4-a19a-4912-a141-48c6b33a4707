import { type FC } from 'react';
import { FlatList } from 'react-native';
import { type MessageCaseMetadata, MessageCaseMetadataItem } from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';

type Props = CreateOmittedFlatListProps<MessageCaseMetadata> & {
  caseMetadata: MessageCaseMetadata[];
};

export const MessageCaseMetadataList: FC<Props> = ({
  caseMetadata,
  ...restProps
}) => {
  return (
    <FlatList
      {...restProps}
      keyExtractor={(item) => item?.id}
      data={caseMetadata}
      renderItem={({ item }) => <MessageCaseMetadataItem caseMetadata={item} />}
    />
  );
};
