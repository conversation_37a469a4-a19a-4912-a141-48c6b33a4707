import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  type MessageCaseMetadata,
  CustomText,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';

type Props = {
  caseMetadata: MessageCaseMetadata;
  containerStyle?: ViewStyle;
};

export const MessageCaseMetadataItem: FC<Props> = ({
  caseMetadata,
  containerStyle,
}) => {
  const { name, description } = caseMetadata || {};

  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.containerBody}>
        <CustomText style={styles.name}>{name}</CustomText>
        <CustomText style={styles.description}> {description}</CustomText>
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      borderTopWidth: 1,
      borderTopColor: color.BORDER_COLOR_FORM,
    },
    containerBody: {
      flexDirection: 'row',
      gap: SPACING.XL,
      paddingVertical: SPACING.M,
      paddingHorizontal: SPACING.M,
    },
    name: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DIMMED,
    },
    description: {
      flex: 1,
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DEFAULT,
    },
  });

  return { styles, color };
};
