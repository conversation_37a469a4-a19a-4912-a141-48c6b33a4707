import { type FC, type ReactNode } from 'react';
import { StyleSheet } from 'react-native';
import {
  type Attachment,
  type Theme,
  Modal,
  AttachmentList,
  BUTTON_DEFAULT_VARIANTS,
  useThemeAwareObject,
} from 'b-ui-lib';

export type CommentAttachmentListModalProps = {
  isVisible: boolean;
  handleClose: () => void;
  title: string | ReactNode;
  attachments: Attachment[];
  handleDownloadAttachment?: (attachmentsId: string) => void;
  handleDownloadAllAttachments?: () => void;
};

export const CommentAttachmentListModal: FC<
  CommentAttachmentListModalProps
> = ({
  isVisible,
  handleClose,
  title,
  attachments,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
}) => {
  const MODAL_BUTTONS = [
    {
      title: `Download All (${attachments?.length})`,
      onPress: () =>
        handleDownloadAllAttachments && handleDownloadAllAttachments(),
      variant: BUTTON_DEFAULT_VARIANTS.secondary,
    },
  ];
  const { styles } = useThemeAwareObject((theme) => createStyles(theme));

  return (
    <Modal
      isVisible={isVisible}
      isCloseIconVisible
      handleClose={handleClose}
      title={title ? title : 'Attachments'}
      buttonsArray={MODAL_BUTTONS}
      componentStyles={{
        buttonContainer: styles.buttonContainer,
      }}
    >
      <AttachmentList
        attachments={attachments}
        handleDownload={(attachmentId) =>
          handleDownloadAttachment && handleDownloadAttachment(attachmentId)
        }
      />
    </Modal>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    buttonContainer: {
      backgroundColor: color.MESSAGE_CATEGORY_ATTACHMENTS_BUTTON,
      borderRadius: 6,
    },
  });

  return { styles, color };
};
