import { type FC, type ReactNode } from 'react';
import ContentLoader, { Circle, Rect } from 'react-content-loader/native';
import {
  type AvatarEmail,
  type Theme,
  useThemeAwareObject,
  Modal,
  AvatarEmailList,
  BenefitIconSet,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
} from 'b-ui-lib';
import { StyleSheet, View } from 'react-native';

export type CommentRecipientsListModalProps = {
  isVisible: boolean;
  handleClose: () => void;
  title: string | ReactNode;
  notifiedUsers: AvatarEmail[];
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string | null;
};

const HeaderTitle = () => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      <BenefitIconSet name="user" size={16} color={color.TEXT_DEFAULT} />
      <CustomText style={styles.title}>Viewers</CustomText>
    </View>
  );
};

const ModalBody = ({
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  notifiedUsers,
}: {
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string | null;
  notifiedUsers: AvatarEmail[];
}) => {
  const { color } = useThemeAwareObject(createStyles);

  if (fetchNotifiedUsersLoading) {
    return (
      <View>
        {[...Array(2)].map((_, index) => (
          <ContentLoader
            key={index}
            width="100%"
            height={90}
            speed={1}
            backgroundColor={color.SKELETON_BACKGROUND}
            foregroundColor={color.SKELETON_FOREGROUND}
          >
            {/* Icon on the left */}
            <Circle cx="24" cy="40" r="16" />

            {/* Reference (e.g. "CAS-1662") */}
            <Rect x="60" y="20" rx="4" ry="4" width="60" height="12" />

            {/* Title (e.g. "Navios // LMS Requests and Certificates") */}
            <Rect x="60" y="38" rx="4" ry="4" width="200" height="12" />

            {/* Assigned to (e.g. "#Username#") */}
            <Rect x="60" y="56" rx="4" ry="4" width="120" height="12" />

            {/* Date on the right (e.g. "Sep 12, 12:23") */}
            <Rect x="300" y="20" rx="4" ry="4" width="80" height="12" />
          </ContentLoader>
        ))}
      </View>
    );
  }

  if (fetchNotifiedUsersError) {
    return (
      <CustomText>{`Fetching notified users failed with error: \n${fetchNotifiedUsersError}`}</CustomText>
    );
  }

  return <AvatarEmailList emails={notifiedUsers} />;
};

export const CommentRecipientsListModal: FC<
  CommentRecipientsListModalProps
> = ({
  isVisible,
  handleClose,
  title,
  notifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
}) => {
  return (
    <Modal
      isVisible={isVisible}
      isCloseIconVisible
      handleClose={handleClose}
      title={title ? title : <HeaderTitle />}
    >
      <ModalBody
        fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
        fetchNotifiedUsersError={fetchNotifiedUsersError}
        notifiedUsers={notifiedUsers}
      />
    </Modal>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: 10,
      alignItems: 'center',
    },
    title: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
  });

  return { styles, color };
};
