import React, { useState, type MutableRefObject } from 'react';
import {
  type TextStyle,
  type KeyboardTypeOptions,
  StyleSheet,
  TextInput,
  View,
  Pressable,
  type TextInputProps,
} from 'react-native';
import {
  BenefitIconSet,
  CustomText,
  FONT_SIZES,
  useThemeAwareObject,
} from 'b-ui-lib';
import { SPACING } from 'b-ui-lib';
import type { Theme } from 'b-ui-lib';

type Error = {
  message: string;
};

export type InputProps = TextInputProps & {
  value: string;
  onChangeText: (text: string) => void;
  label?: string;
  placeholder?: string;
  isPassword?: boolean;
  containerStyle?: TextStyle;
  error?: Error;
  inputRef?: MutableRefObject<TextInput>;
  showSoftInputOnFocus?: boolean;
  keyboardType?: KeyboardTypeOptions;
  multiline?: boolean;
  numberOfLines?: number;
  inputStyle?: TextStyle;
};

export const Input: React.FC<InputProps> = ({
  value,
  onChangeText,
  label,
  placeholder,
  isPassword,
  containerStyle,
  error,
  inputRef,
  showSoftInputOnFocus = true,
  keyboardType,
  multiline,
  numberOfLines,
  inputStyle,
  ...restProps
}) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);

  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isFocused, error, label)
  );

  const handleInputFocus = () => setIsFocused(true);
  const handleInputBlur = () => setIsFocused(false);

  const handleTogglePassword = () => setShowPassword(!showPassword);

  return (
    <View style={styles.container}>
      <View style={[styles.body, containerStyle]}>
        {label && <CustomText style={styles.labelText}>{label}</CustomText>}

        <View style={styles.inputContainer}>
          <TextInput
            {...restProps}
            multiline={multiline}
            numberOfLines={numberOfLines}
            style={[styles.input, inputStyle]}
            value={value}
            onChangeText={onChangeText}
            secureTextEntry={isPassword && !showPassword}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            autoCapitalize="none"
            placeholder={error ? `${placeholder} *` : placeholder}
            placeholderTextColor={error ? color.RED : color.TEXT_DEFAULT}
            ref={inputRef}
            showSoftInputOnFocus={showSoftInputOnFocus}
            keyboardType={keyboardType}
          />
          {isPassword && (
            <Pressable onPress={handleTogglePassword}>
              <BenefitIconSet
                name={showPassword ? 'eye' : 'eye-off'}
                size={16}
              />
            </Pressable>
          )}
        </View>
      </View>

      {error && <CustomText style={styles.error}>{error?.message}</CustomText>}
    </View>
  );
};

const createStyles = (
  { color }: Theme,
  isFocused: boolean,
  error?: Error,
  label?: string
) => {
  const styles = StyleSheet.create({
    container: {
      gap: SPACING.TEN,
    },
    body: {
      borderWidth: 1,
      borderColor: error ? color.ERROR : color.BORDER_COLOR_FORM,
      color: error ? color.ERROR : color.TEXT_DEFAULT,
      borderRadius: SPACING.SIX,
      paddingVertical: label ? SPACING.XS : SPACING.M,
      paddingHorizontal: SPACING.S,
      backgroundColor: isFocused
        ? color.PRESSABLE
        : color.MESSAGE_ITEM__BACKGROUND,
    },
    labelText: {
      color: color.TEXT_SEARCH_INVERTED,
    },
    inputContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    input: {
      flex: 1,
      padding: 0,
      margin: 0,
      fontSize: FONT_SIZES.TWELVE,
      color: error ? color.RED : color.TEXT_DEFAULT,
    },
    error: {
      color: color.RED,
    },
  });

  return { styles, color };
};
