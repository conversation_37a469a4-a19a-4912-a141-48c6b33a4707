import { type FC } from 'react';
import {
  Image,
  type ImageSourcePropType,
  type ImageStyle,
  type StyleProp,
} from 'react-native';

export type LogoProps = {
  source?: ImageSourcePropType;
  width?: number;
  height?: number;
  style?: StyleProp<ImageStyle>;
};

export const Logo: FC<LogoProps> = ({
  source,
  width = 210,
  height = 40,
  style,
}) => {
  return (
    <Image
      source={source}
      style={[
        {
          width,
          height,
        },
        style,
      ]}
      resizeMode="contain"
    />
  );
};
