import React, { type ReactNode } from 'react';
import {
  type StyleProp,
  type ViewStyle,
  type TextStyle,
  StyleSheet,
  View,
} from 'react-native';
import {
  type Theme,
  Button,
  BUTTON_DEFAULT_VARIANTS,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  IconButton,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';
import ReactNativeModal from 'react-native-modal';

type componentStyles = {
  container?: StyleProp<ViewStyle>;
  title?: StyleProp<TextStyle>;
  subtitle?: StyleProp<TextStyle>;
  buttonContainer?: StyleProp<TextStyle>;
};

type Button = {
  title: string;
  onPress: () => void;
  variant?: (typeof BUTTON_DEFAULT_VARIANTS)[keyof typeof BUTTON_DEFAULT_VARIANTS];
};

export type ModalProps = {
  isVisible: boolean;
  isCloseIconVisible?: boolean;
  handleClose: () => void;
  title?: string | ReactNode;
  subtitle?: string;
  children?: ReactNode;
  buttonsArray?: Button[];
  componentStyles?: componentStyles;
};

export const Modal: React.FC<ModalProps> = (props) => {
  const {
    isVisible = false,
    isCloseIconVisible = false,
    handleClose,
    title,
    subtitle,
    children,
    buttonsArray,
    componentStyles,
    ...restProps
  } = props || {};
  const { styles } = useThemeAwareObject((theme) =>
    createStyles(theme, isCloseIconVisible)
  );

  return (
    <ReactNativeModal
      isVisible={isVisible}
      hasBackdrop
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropTransitionOutTiming={0}
      backdropOpacity={0.3}
      onBackButtonPress={handleClose}
      onBackdropPress={handleClose}
      {...restProps}
    >
      <View style={[styles.container, componentStyles?.container]}>
        <View style={styles.header}>
          {title && typeof title === 'string' && (
            <CustomText style={[styles.title, componentStyles?.title]}>
              {title}
            </CustomText>
          )}
          {title && typeof title !== 'string' && <>{title}</>}

          {isCloseIconVisible && (
            <IconButton
              name="x-circle-filled"
              size={20}
              onPress={handleClose}
              containerStyle={styles.closeIcon}
            />
          )}
        </View>
        {subtitle && (
          <CustomText style={[styles.subtitle, componentStyles?.subtitle]}>
            {subtitle}
          </CustomText>
        )}
        {children && children}

        <View style={componentStyles?.buttonContainer}>
          {buttonsArray?.length &&
            buttonsArray.length > 0 &&
            buttonsArray.map((button) => <Button {...button} />)}
        </View>
      </View>
    </ReactNativeModal>
  );
};

const createStyles = ({ color }: Theme, isCloseIconVisible: boolean) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MODAL_BACKGROUND_COLOR,
      paddingHorizontal: SPACING.M,
      paddingVertical: SPACING.TEN,
      gap: SPACING.TEN,
      borderRadius: SPACING.THIRTEEN,
      minWidth: 150,
    },
    header: {
      flexDirection: 'row',
      justifyContent: isCloseIconVisible ? 'space-between' : 'center',
    },
    title: {
      alignSelf: 'center',
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
    closeIcon: {
      padding: SPACING.XXS,
    },
    subtitle: {
      alignSelf: 'center',
      fontSize: FONT_SIZES.TWELVE,
      fontWeight: FONT_WEIGHTS.NORMAL,
      marginVertical: 4,
    },
  });

  return { styles, color };
};
