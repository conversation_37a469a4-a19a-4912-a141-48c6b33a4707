import { type FC } from 'react';
import { StyleSheet, View } from 'react-native';
import { type Theme, useThemeAwareObject } from 'b-ui-lib';
import ContentLoader, { Circle, Rect } from 'react-content-loader/native';

type Props = {
  backgroundColor?: string;
};

export const MessageInfoDataSkeleton: FC<Props> = ({ backgroundColor }) => {
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, backgroundColor)
  );

  return (
    <View style={styles.container}>
      <ContentLoader
        width="100%"
        height={80}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* Avatar */}
        <Circle cx="16" cy="16" r="16" />

        {/* Text placeholders */}
        <Rect x="47" y="3" rx="4" ry="4" width="50" height="11" />
        <Rect x="47" y="23" rx="4" ry="4" width="100" height="11" />
        <Rect x="47" y="47" rx="4" ry="4" width="115" height="11" />
        <Rect x="47" y="67" rx="4" ry="4" width="140" height="11" />

        {/*ArrowIcon on the right*/}
        <Rect x="350" y="60" rx="4" ry="4" width="15" height="13" />

        {/*arrowIcon inOut*/}
        <Rect x="10" y="59" rx="2" ry="2" width="15" height="15" />
      </ContentLoader>
    </View>
  );
};

const createStyles = ({ color }: Theme, backgroundColor?: string) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: backgroundColor ? backgroundColor : color.BACKGROUND,
    },
  });

  return { styles, color };
};
