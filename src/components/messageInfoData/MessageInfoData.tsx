import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  CustomText,
  SPACING,
  useThemeAwareObject,
  IconButton,
  MessageAvatar,
  type InOut,
} from 'b-ui-lib';
import Animated, {
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { MessageInfoDataSkeleton } from './MessageInfoDataSkeleton';

type Props = {
  isHidden?: boolean;
  isSkeletonLoading?: boolean;
  skeletonBackgroundColor?: string;
  avatarName: string;
  inOut: InOut;
  from: string;
  tos: string;
  ccs: string;
  bccs: string;
  containerStyle?: ViewStyle;
};

export const MessageInfoData: FC<Props> = ({
  isHidden = false,
  isSkeletonLoading = false,
  skeletonBackgroundColor,
  avatarName,
  inOut,
  from,
  tos,
  ccs,
  bccs,
  containerStyle,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const open = useSharedValue(false);
  const onPress = () => {
    open.value = !open.value;
  };

  const height = useSharedValue(0);
  const derivedHeight = useDerivedValue(() =>
    withTiming(height.value * Number(open.value), {
      duration: 500,
    })
  );
  const bodyStyle = useAnimatedStyle(() => ({
    height: derivedHeight.value,
  }));

  if (isHidden) {
    return null;
  }

  if (isSkeletonLoading) {
    return (
      <MessageInfoDataSkeleton backgroundColor={skeletonBackgroundColor} />
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <MessageAvatar avatarName={avatarName} inOut={inOut} />

      <View style={styles.messageInfoContainer}>
        <CustomText style={styles.boldText} numberOfLines={1}>
          {from}
        </CustomText>

        <View style={styles.fromContainer}>
          <CustomText style={styles.fromText}>
            From: <CustomText style={styles.toText}>{from}</CustomText>
          </CustomText>

          {tos && (
            <CustomText style={styles.fromText}>
              To: <CustomText style={styles.toText}>{tos}</CustomText>
            </CustomText>
          )}

          <Animated.View style={[styles.animatedView, bodyStyle]}>
            <View
              onLayout={(e) => {
                height.value = e.nativeEvent.layout.height;
              }}
              style={styles.wrapper}
            >
              {ccs && (
                <CustomText style={styles.fromText}>
                  Cc: <CustomText style={styles.toText}>{ccs}</CustomText>
                </CustomText>
              )}

              {bccs && (
                <CustomText style={styles.fromText}>
                  Bcc: <CustomText style={styles.toText}>{bccs}</CustomText>
                </CustomText>
              )}
            </View>
          </Animated.View>
        </View>
      </View>

      {(bccs || ccs) && (
        <View style={styles.arrowDownIcon}>
          <IconButton
            name="chevron-down"
            color={color.MESSAGE_FLAG}
            size={22}
            onPress={onPress}
          />
        </View>
      )}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: SPACING.M,
    },
    messageInfoContainer: {
      flex: 1,
      gap: SPACING.XS,
    },
    fromContainer: {
      gap: 1,
    },
    boldText: {
      fontWeight: '700',
    },
    fromText: {
      fontSize: 12,
      lineHeight: 18,
    },
    toText: {
      color: color.BLUE_DEFAULT,
      lineHeight: 18,
    },
    animatedView: {
      width: '100%',
      overflow: 'hidden',
    },
    wrapper: {
      width: '100%',
      position: 'absolute',
    },
    arrowDownIcon: {
      marginLeft: 'auto',
      alignSelf: 'flex-end',
    },
  });

  return { styles, color };
};
