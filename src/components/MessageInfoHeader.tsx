import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  CustomText,
  SPACING,
  useThemeAwareObject,
  MessageDate,
  BenefitIconSet,
} from 'b-ui-lib';
import { TEST_IDS } from '../constants/testIds';

type Props = {
  subject: string;
  sentDate: string;
  notificationIcon?: string;
  containerStyle?: ViewStyle;
};

export const MessageInfoHeader: FC<Props> = ({
  subject,
  sentDate,
  notificationIcon,
  containerStyle,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, containerStyle]}>
      {notificationIcon && (
        <BenefitIconSet
          name={notificationIcon}
          size={24}
          color={color.NOTIFICATION_ICON}
        />
      )}

      <CustomText testID={TEST_IDS.messageSubject} style={styles.subjectText}>
        {subject}
      </CustomText>

      <MessageDate sentDate={sentDate} />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: SPACING.M,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    subjectText: {
      fontSize: 20,
      fontWeight: 'bold',
      flex: 1,
    },
  });

  return { styles, color };
};
