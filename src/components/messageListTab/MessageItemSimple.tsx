import { type FC } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  type SimpleMessage,
  type Theme,
  useThemeAwareObject,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  MessageAvatar,
  SPACING,
  MessageDate,
} from 'b-ui-lib';

type Props = {
  message: SimpleMessage;
  handleTapMessage?: (messageId: string) => void;
};

export const MessageItemSimple: FC<Props> = ({ message, handleTapMessage }) => {
  const { sentDate, username, from, subject, avatarName, inOut } =
    message || {};
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <Pressable
      onPress={() => handleTapMessage && handleTapMessage(message.id)}
      style={styles.container}
    >
      <MessageAvatar avatarName={avatarName} inOut={inOut} />

      <View style={styles.messageInfoContainer}>
        <CustomText style={styles.fromText} numberOfLines={1}>
          "{username}" - &lt;{from}&gt;
        </CustomText>

        <CustomText style={styles.subjectText} numberOfLines={1}>
          {subject}
        </CustomText>
      </View>

      <View style={styles.dateContainer}>
        <MessageDate sentDate={sentDate} />
      </View>
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 16,
      padding: SPACING.M,
      borderTopWidth: 1,
      borderTopColor: color.PRESSABLE_HOVER,
    },
    messageInfoContainer: {
      flex: 1,
      gap: SPACING.SIX,
      paddingTop: SPACING.TEN,
    },
    dateContainer: {
      paddingTop: SPACING.TEN,
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    fromText: {
      fontSize: FONT_SIZES.FOURTEEN,
      lineHeight: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.TEXT_DEFAULT,
    },
    subjectText: {
      fontSize: FONT_SIZES.TWELVE,
      lineHeight: FONT_SIZES.TWELVE,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.MESSAGE_ITEM__SUBJECT,
    },
  });

  return { styles, color };
};
