import { MESSAGE_IN_OUT, MESSAGE_TYPE } from '../../constants/message';

export type SimpleMessage = {
  id: string;
  messageKey: string;
  type:
    | typeof MESSAGE_TYPE.email
    | typeof MESSAGE_TYPE.fax
    | typeof MESSAGE_TYPE.meeting
    | typeof MESSAGE_TYPE.emailMeeting;
  inOut:
    | typeof MESSAGE_IN_OUT.one
    | typeof MESSAGE_IN_OUT.two
    | typeof MESSAGE_IN_OUT.three;
  attachmentsCount?: number;
  from: string;
  to: string;
  cc: string;
  subject: string;
  avatarName: string;
  sentDate: string;
  username: string;
};
