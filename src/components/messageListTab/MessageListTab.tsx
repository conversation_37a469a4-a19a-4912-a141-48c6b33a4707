import { type FC } from 'react';
import {
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
  type ViewStyle,
} from 'react-native';
import {
  type SimpleMessage,
  type Theme,
  CustomText,
  ListEmptyComponent,
  SPACING,
  TabTitle,
  useThemeAwareObject,
} from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';
import { MessageItemSimple } from './MessageItemSimple';
import ContentLoader, { Circle, Rect } from 'react-content-loader/native';

type Props = CreateOmittedFlatListProps<SimpleMessage> & {
  messages?: SimpleMessage[];
  tabTitle?: string;
  handleTapMessage?: (messageId: string) => void;
  handleRefreshList?: () => void;
  isLoading?: boolean;
  errorMessage?: string;
  containerStyle?: ViewStyle;
};

export const MessageListTab: FC<Props> = ({
  messages,
  tabTitle,
  handleTapMessage,
  handleRefreshList,
  isLoading = false,
  errorMessage,
  containerStyle,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (isLoading) {
    return (
      <View style={[styles.container, containerStyle]}>
        <TabTitle
          title={tabTitle || 'Emails Related'}
          count={messages?.length?.toString() ?? '0'}
          containerStyles={styles.tabTitle}
        />

        <View>
          {[...Array(2)].map((_, index) => (
            <ContentLoader
              key={index}
              width="100%"
              height={80}
              speed={1}
              backgroundColor={color.SKELETON_BACKGROUND}
              foregroundColor={color.SKELETON_FOREGROUND}
              style={styles.skeleton}
            >
              {/* Avatar */}
              <Circle cx="30" cy="30" r="15" />

              {/* Display Name */}
              <Rect x="60" y="25" rx="5" ry="4" width="240" height="12" />

              {/* Email Subject */}
              <Rect x="60" y="45" rx="5" ry="4" width="120" height="12" />

              {/* Small icon or indicator on the right */}
              <Rect x="350" y="25" rx="3" ry="3" width="20" height="20" />

              <Rect x="25" y="55" rx="3" ry="3" width="10" height="17" />
            </ContentLoader>
          ))}
        </View>
      </View>
    );
  }

  if (errorMessage) {
    return (
      <View style={[styles.container, containerStyle]}>
        <TabTitle
          title={tabTitle || 'Emails Related'}
          count={messages?.length?.toString() ?? '0'}
          containerStyles={styles.tabTitle}
        />
        <CustomText style={styles.errorMessage}>{errorMessage}</CustomText>
      </View>
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <TabTitle
        title={tabTitle || 'Emails Related'}
        count={messages?.length?.toString() ?? '0'}
        containerStyles={styles.tabTitle}
      />

      <FlatList
        {...restProps}
        data={messages}
        renderItem={({ item }) => (
          <MessageItemSimple
            message={item}
            handleTapMessage={handleTapMessage && handleTapMessage}
          />
        )}
        ListEmptyComponent={<ListEmptyComponent emptyText={''} />}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={() => handleRefreshList && handleRefreshList()}
            tintColor={color.BLACK}
          />
        }
      />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      padding: SPACING.M,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    tabTitle: {
      paddingBottom: SPACING.M,
    },
    errorMessage: {
      alignSelf: 'center',
      paddingTop: SPACING.M,
    },
    skeleton: {
      marginBottom: 16,
    },
  });

  return { styles, color };
};
