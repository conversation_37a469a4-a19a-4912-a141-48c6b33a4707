import { type FC } from 'react';
import { Pressable, StyleSheet, type ViewStyle } from 'react-native';
import { type Theme, CustomText, useThemeAwareObject, SPACING } from 'b-ui-lib';

type Props = {
  suggestion: string;
  handleSuggestionPress?: (suggestion: string) => void;
  testStyles?: ViewStyle;
};

export const SuggestionItem: FC<Props> = ({
  suggestion,
  handleSuggestionPress,
  testStyles,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <Pressable
      onPress={() => handleSuggestionPress && handleSuggestionPress(suggestion)}
    >
      <CustomText style={[styles.suggestionText, testStyles]} numberOfLines={1}>
        {suggestion}
      </CustomText>
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    suggestionText: {
      paddingVertical: SPACING.S,
      paddingHorizontal: SPACING.M,
    },
  });

  return { styles, color };
};
