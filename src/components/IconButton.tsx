import React from 'react';
import {
  type ViewStyle,
  type GestureResponderEvent,
  StyleSheet,
  Pressable,
  type PressableProps,
} from 'react-native';
import { type Theme, BenefitIconSet, useThemeAwareObject } from 'b-ui-lib';

type Props = PressableProps & {
  name: string;
  size?: number;
  color?: string;
  onPress?: (e: GestureResponderEvent) => void;
  containerStyle?: ViewStyle;
  testID?: string;
};

export const IconButton: React.FC<Props> = ({
  name,
  size,
  color,
  onPress,
  containerStyle,
  testID,
  ...restProps
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <Pressable
      {...restProps}
      testID={testID}
      style={[styles.container, containerStyle]}
      onPress={onPress}
    >
      <BenefitIconSet name={name} size={size ?? 24} color={color} />
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
  });

  return { styles, color };
};
