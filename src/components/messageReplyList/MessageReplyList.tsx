import { type FC } from 'react';
import { FlatList } from 'react-native';
import { type MessageReply, MessageReplyItem } from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';

type Props = CreateOmittedFlatListProps<MessageReply> & {
  replies: MessageReply[];
};

export const MessageReplyList: FC<Props> = ({ replies, ...restProps }) => {
  return (
    <FlatList
      {...restProps}
      data={replies}
      renderItem={({ item }) => (
        <MessageReplyItem
          key={item.id}
          avatarName={item.avatarName}
          username={item.username}
          from={item.from}
          date={item.date}
        />
      )}
    />
  );
};
