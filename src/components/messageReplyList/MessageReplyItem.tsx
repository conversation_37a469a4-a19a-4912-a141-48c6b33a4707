import type { FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  Avatar,
  CustomText,
  FONT_SIZES,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import { formatDate } from '../../helpers/formatDate';

type Props = {
  avatarName?: string;
  username?: string;
  from?: string;
  date?: string;
  containerStyle?: ViewStyle;
};

export const MessageReplyItem: FC<Props> = ({
  avatarName,
  username,
  from,
  date,
  containerStyle,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={{}}>
        <Avatar
          name={avatarName}
          backgroundColor={color.AVATAR_BACKGROUND}
          style={{ container: styles.avatar }}
        />
      </View>

      <View style={styles.textContainer}>
        <CustomText style={styles.username}>{username}</CustomText>

        {from && (
          <CustomText style={styles.fromTextContainer}>
            From: <CustomText style={styles.fromText}>{from}</CustomText>
          </CustomText>
        )}

        <CustomText style={styles.dateText}>{formatDate(date)}</CustomText>
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      paddingVertical: SPACING.S,
      borderTopWidth: 1,
      borderTopColor: color.BORDER_COLOR_FORM,
    },
    avatar: {
      marginRight: SPACING.M,
    },
    textContainer: {
      gap: SPACING.XXS,
    },
    username: {
      fontWeight: '700',
    },
    fromTextContainer: {
      fontSize: 12,
      color: color.TEXT_DEFAULT_INVETRED,
    },
    fromText: {
      color: color.MESSAGE_FLAG,
    },
    dateText: {
      fontSize: FONT_SIZES.TEN,
      lineHeight: FONT_SIZES.FOURTEEN,
      color: color.TEXT_GREY,
    },
  });

  return { styles, color };
};
