import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle, Pressable } from 'react-native';
import {
  type Theme,
  CustomText,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
  BenefitIconSet,
  FONT_WEIGHTS,
} from 'b-ui-lib';
import { formatDate } from '../../helpers/formatDate';
import { DISPLAY_MODE } from './caseDisplayMode';

type Props = {
  title: string;
  date: string;
  casReference: string;
  assignedTo?: string;
  from?: string;
  containerStyle?: ViewStyle;
  onPress?: () => void;
  displayMode?: (typeof DISPLAY_MODE)[keyof typeof DISPLAY_MODE];
};

export const CaseItem: FC<Props> = ({
  title,
  date,
  casReference,
  assignedTo,
  from,
  containerStyle,
  onPress,
  displayMode,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const displayModeValue = displayMode ? displayMode : DISPLAY_MODE.case;

  return (
    <Pressable style={[styles.container, containerStyle]} onPress={onPress}>
      <View style={styles.wrapper}>
        <View style={styles.briefcaseIcon}>
          <BenefitIconSet name="briefcase" size={16} color={color.WHITE} />
        </View>

        <View>
          <CustomText style={styles.ref} numberOfLines={1}>
            {casReference}
          </CustomText>
          <CustomText style={styles.title} numberOfLines={2}>
            {title}
          </CustomText>
          {displayModeValue === DISPLAY_MODE.case && assignedTo && (
            <CustomText style={styles.assigned} numberOfLines={1}>
              Assigned to:{' '}
              <CustomText style={styles.assignedText}>{assignedTo}</CustomText>
            </CustomText>
          )}
          {displayModeValue === DISPLAY_MODE.linkedCase && from && (
            <CustomText style={styles.assigned} numberOfLines={1}>
              From: <CustomText style={styles.assignedText}>{from}</CustomText>
            </CustomText>
          )}
        </View>
      </View>

      {date && (
        <View style={styles.dateContainer}>
          <CustomText style={styles.dateText}>Added on</CustomText>
          <CustomText style={styles.date}> {formatDate(date)}</CustomText>
        </View>
      )}
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      borderTopWidth: 1,
      borderTopColor: color.PRESSABLE_HOVER,
      flexDirection: 'row',
      gap: SPACING.L,
      paddingVertical: SPACING.EIGHTEEN,
      paddingHorizontal: SPACING.TWENTY_TWO,
    },
    wrapper: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: SPACING.M,
      flex: 4,
    },
    briefcaseIcon: {
      backgroundColor: color.ORANGE_BRIEFCASE,
      padding: SPACING.XS,
      borderRadius: SPACING.M,
    },
    ref: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
    title: {
      flexWrap: 'wrap',
      color: color.TEXT_DEFAULT,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
    assigned: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
    assignedText: {
      fontWeight: FONT_WEIGHTS.BOLD,
    },
    dateContainer: {
      flex: 2,
      alignItems: 'flex-end',
    },
    dateText: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_GREY_DARKER,
    },
    date: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_GREY,
    },
  });

  return { styles, color };
};
