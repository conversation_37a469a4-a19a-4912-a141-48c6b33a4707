import { type FC } from 'react';
import {
  FlatList,
  RefreshControl,
  StyleSheet,
  View,
  type ViewStyle,
} from 'react-native';
import {
  type Theme,
  CustomText,
  SPACING,
  useThemeAwareObject,
  CaseItem,
  type Case,
} from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';
import ContentLoader, { Circle, Rect } from 'react-content-loader/native';
import type { DISPLAY_MODE } from './caseDisplayMode';

type Props = CreateOmittedFlatListProps<Case> & {
  cases?: Case[];
  handleTapCase?: (caseId: string) => void;
  handleRefreshList?: () => void;
  isLoading?: boolean;
  errorMessage?: string;
  loadMore?: () => void;
  listFooterComponent?: React.ReactElement | null;
  displayMode?: (typeof DISPLAY_MODE)[keyof typeof DISPLAY_MODE];
  containerStyle?: ViewStyle;
};

export const CaseList: FC<Props> = ({
  cases,
  handleTapCase,
  handleRefreshList,
  isLoading = false,
  errorMessage,
  loadMore,
  listFooterComponent,
  displayMode,
  containerStyle,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (isLoading) {
    return (
      <View style={styles.skeletonContainer}>
        {[...Array(3)].map((_, index) => (
          <ContentLoader
            key={index}
            width="100%"
            height={90}
            speed={1}
            backgroundColor={color.SKELETON_BACKGROUND}
            foregroundColor={color.SKELETON_FOREGROUND}
          >
            {/* Icon on the left */}
            <Circle cx="24" cy="40" r="16" />

            {/* Reference (e.g. "CAS-1662") */}
            <Rect x="60" y="20" rx="4" ry="4" width="60" height="12" />

            {/* Title (e.g. "Navios // LMS Requests and Certificates") */}
            <Rect x="60" y="38" rx="4" ry="4" width="200" height="12" />

            {/* Assigned to (e.g. "#Username#") */}
            <Rect x="60" y="56" rx="4" ry="4" width="120" height="12" />

            {/* Date on the right (e.g. "Sep 12, 12:23") */}
            <Rect x="300" y="20" rx="4" ry="4" width="80" height="12" />
          </ContentLoader>
        ))}
      </View>
    );
  }

  if (errorMessage) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.errorMessage}>{errorMessage}</CustomText>
      </View>
    );
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <FlatList
        {...restProps}
        style={styles.container}
        data={cases}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={() => handleRefreshList && handleRefreshList()}
            tintColor={color.BLACK}
          />
        }
        keyExtractor={(item) => item.CAS_Guid}
        renderItem={({ item }) => {
          return (
            <CaseItem
              title={item.CAS_Title}
              casReference={item.CAS_Reference}
              assignedTo={item.USR_Name_AssignedTo}
              from={item.CreatedUserName}
              date={item.CAS_CreatedTimestamp}
              onPress={() => handleTapCase && handleTapCase(item.CAS_Guid)}
              displayMode={displayMode}
            />
          );
        }}
        ListEmptyComponent={
          <View
            style={{
              justifyContent: 'center',
              alignItems: 'center',
              paddingTop: 70,
            }}
          >
            <CustomText>List Empty</CustomText>
          </View>
        }
        onEndReached={loadMore}
        ListFooterComponent={listFooterComponent}
      />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
    errorMessage: {
      color: color.ERROR,
      alignSelf: 'center',
      paddingTop: SPACING.L,
    },
    skeletonContainer: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      paddingTop: 10,
      paddingHorizontal: 15,
    },
  });

  return { styles, color };
};
