import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  BenefitIconSet,
  MESSAGE_IN_OUT,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';

type Props = {
  inOut:
    | typeof MESSAGE_IN_OUT.one
    | typeof MESSAGE_IN_OUT.two
    | typeof MESSAGE_IN_OUT.three;
  containerStyle?: ViewStyle;
};

export const MessageArrow: FC<Props> = ({ inOut, containerStyle }) => {
  const { styles, color } = useThemeAwareObject((theme) => createStyles(theme));

  const MESSAGE_ARROW_ICON: { [key: number]: { name: string; color: string } } =
    {
      1: {
        name: 'arrow-down',
        color: color.MESSAGE_ITEM__ARROW_DOWN,
      },
      2: {
        name: 'arrow-up',
        color: color.OUTBOUND_EMAIL_ARROW_ICON,
      },
      3: {
        name: 'arrow-right',
        color: color.OUTBOUND_EMAIL_ARROW_ICON,
      },
    };

  return (
    <View style={[styles.container, containerStyle]}>
      <BenefitIconSet
        name={MESSAGE_ARROW_ICON[inOut]?.name ?? 'arrow-down'}
        size={24}
        color={MESSAGE_ARROW_ICON[inOut]?.color}
      />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
  });

  return { styles, color };
};
