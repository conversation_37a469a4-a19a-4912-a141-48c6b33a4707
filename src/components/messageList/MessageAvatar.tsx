import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  Avatar,
  type InOut,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import { MessageArrow } from './MessageArrow';

type Props = {
  avatarName?: string;
  inOut: InOut;
  containerStyle?: ViewStyle;
};

export const MessageAvatar: FC<Props> = ({
  avatarName,
  inOut,
  containerStyle,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, containerStyle]}>
      <Avatar name={avatarName} />
      <MessageArrow inOut={inOut} />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: SPACING.TEN,
    },
  });

  return { styles, color };
};
