import { type FC, useMemo } from 'react';
import { StyleSheet, type TextStyle, View, type ViewStyle } from 'react-native';
import {
  CustomText,
  FONT_SIZES,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import { TEST_IDS } from '../../constants/testIds';

type Props = {
  sentDate: string;
  style?: {
    container: ViewStyle;
    text: TextStyle;
  };
};

export const MessageDate: FC<Props> = ({ sentDate, style }) => {
  const { styles } = useThemeAwareObject((theme) => createStyles(theme));

  const dateTime = useMemo(() => {
    // TODO Below is a check for utc date strings. Maybe we dont need this.
    // Check if the sentDate string ends with "Z" or "z" to determine if it already includes a UTC timezone indicator
    // This ensures new Date(dateString) interprets the time correctly with the proper timezone
    // const hasSpecificCharacter = /[zZ]$/.test(sentDate);
    // const dateString = hasSpecificCharacter ? sentDate : `${sentDate}Z`;

    const inputDate = new Date(sentDate);
    const now = new Date();

    const time = inputDate.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const isToday =
      inputDate.getDate() === now.getDate() &&
      inputDate.getMonth() === now.getMonth() &&
      inputDate.getFullYear() === now.getFullYear();

    const date = isToday
      ? 'Today'
      : inputDate.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        });

    return { date, time };
  }, [sentDate]);

  return (
    <View style={[styles.container, style?.container]}>
      <CustomText
        testID={TEST_IDS.messageTime}
        style={[styles.text, style?.text]}
      >
        {dateTime.time}
      </CustomText>
      <CustomText
        testID={TEST_IDS.messageDate}
        style={[styles.text, style?.text]}
      >
        {dateTime.date}
      </CustomText>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      gap: 4,
      alignItems: 'flex-end',
    },
    text: {
      fontSize: FONT_SIZES.TEN,
      lineHeight: FONT_SIZES.TEN,
      color: color.TEXT_GREY,
    },
  });

  return { styles, color };
};
