import { type FC } from 'react';
import { StyleSheet, View } from 'react-native';
import { CustomText, SPACING, type Theme, useThemeAwareObject } from 'b-ui-lib';

type Props = {
  title: string;
};

export const MessageHeader: FC<Props> = ({ title }) => {
  const { styles } = useThemeAwareObject((theme) => createStyles(theme));

  return (
    <View style={styles.container}>
      <CustomText style={styles.title}>{title}</CustomText>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      paddingHorizontal: SPACING.M,
      backgroundColor: color.PRESSABLE_HOVER,
    },
    title: {
      fontSize: 12,
      color: color.TEXT_DEFAULT,
    },
  });

  return { styles, color };
};
