import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import { BenefitIconSet, type Theme, useThemeAwareObject } from 'b-ui-lib';
import { TEST_IDS } from '../../constants/testIds';

type Props = {
  iconSize?: number;
  hasReplies?: boolean;
  hasAttachments?: boolean;
  hasComments?: boolean;
  hasMetadata?: boolean;
  hasFolders?: boolean;
  hasCases?: boolean;
  containerStyle?: ViewStyle;
  messageSectionIndex?: number; // This is used only for test purposes
};

export const MessageIcons: FC<Props> = ({
  iconSize,
  hasReplies,
  hasAttachments,
  hasComments,
  hasMetadata,
  hasFolders,
  hasCases,
  containerStyle,
  messageSectionIndex,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const MESSAGE_ICONS = [
    {
      name: 'check-circle',
      isVisible: hasReplies,
      testID: `${messageSectionIndex}-${TEST_IDS.messageListBottomIconsHasReplies}`,
    },
    {
      name: 'message',
      isVisible: hasComments,
      testID: `${messageSectionIndex}-${TEST_IDS.messageListBottomIconsHasComments}`,
    },
    {
      name: 'paperclip',
      isVisible: hasAttachments,
      testID: `${messageSectionIndex}-${TEST_IDS.messageListBottomIconsHasAttachments}`,
    },
    {
      name: 'folder',
      isVisible: hasFolders,
      testID: `${messageSectionIndex}-${TEST_IDS.messageListBottomIconsHasFolders}`,
    },
    {
      name: 'tag',
      isVisible: hasMetadata,
      testID: `${messageSectionIndex}-${TEST_IDS.messageListBottomIconsHasMetadata}`,
    },
    {
      name: 'briefcase',
      isVisible: hasCases,
      testID: `${messageSectionIndex}-${TEST_IDS.messageListBottomIconsHasCases}`,
    },
  ];

  return (
    <View style={[styles.container, containerStyle]}>
      {MESSAGE_ICONS.map((icon, index) => {
        if (!icon.isVisible) return null;

        return (
          <BenefitIconSet
            testID={icon.testID}
            key={`${index} ${icon.name}`}
            name={icon.name}
            size={iconSize ?? 14.5}
            color={color.EMAIL_ICON}
          />
        );
      })}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      gap: 15,
      marginTop: 'auto',
    },
  });

  return { styles, color };
};
