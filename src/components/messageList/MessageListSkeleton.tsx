import { type FC } from 'react';
import { StyleSheet, View } from 'react-native';
import { type Theme, useThemeAwareObject } from 'b-ui-lib';
import ContentLoader, { Circle, Rect } from 'react-content-loader/native';

type Props = {};

export const MessageListSkeleton: FC<Props> = () => {
  const { styles, color } = useThemeAwareObject((theme) => createStyles(theme));

  return (
    <View style={styles.container}>
      {/* Create 6 rows to simulate the skeleton */}
      {[...Array(6)].map((_, index) => (
        <ContentLoader
          key={index}
          width="100%"
          height={100}
          speed={1}
          backgroundColor={color.SKELETON_BACKGROUND}
          foregroundColor={color.SKELETON_FOREGROUND}
          style={styles.skeleton}
        >
          {/* Section Header*/}
          <Rect x="0" y="0" rx="4" ry="4" width="100%" height="15" />

          {/* Avatar */}
          <Circle cx="40" cy="45" r="17" />

          {/* Text placeholders */}
          <Rect x="80" y="30" rx="4" ry="4" width="200" height="10" />
          <Rect x="80" y="50" rx="3" ry="3" width="150" height="10" />
          <Rect x="80" y="70" rx="3" ry="3" width="180" height="10" />

          {/* Two small rectangles on the top right, aligned */}
          <Rect x="330" y="30" rx="3" ry="3" width="50" height="10" />
          <Rect x="340" y="50" rx="3" ry="3" width="40" height="10" />

          {/* Small rectangle for flag icon on the bottom right */}
          <Rect x="360" y="90" rx="2" ry="2" width="20" height="10" />

          {/* 6 small rectangles arranged in a row (like an icon with text beside it) */}
          {[...Array(6)].map((_, idx) => (
            <Rect
              key={idx}
              x={80 + idx * 30} // Spread out the 6 rectangles
              y="90"
              rx="3"
              ry="3"
              width="20"
              height="10"
            />
          ))}

          {/* Arrow below avatar */}
          <Rect
            x="33"
            y="85"
            rx="3"
            ry="3"
            width="15"
            height="15"
            fill="#e0e0e0"
          />
        </ContentLoader>
      ))}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    skeleton: {
      marginBottom: 16,
    },
  });

  return { styles, color };
};
