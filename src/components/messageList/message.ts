import { MESSAGE_IN_OUT, MESSAGE_TYPE } from '../../constants/message';

export type Message = {
  id: string;
  messageKey: string;
  sentDate: string;
  username: string;
  from: string;
  to: string;
  subject: string;
  body: string;
  avatarName: string;
  isFlagged: boolean;
  attachmentsCount?: number;
  isViewed: boolean;
  inOut:
    | typeof MESSAGE_IN_OUT.one
    | typeof MESSAGE_IN_OUT.two
    | typeof MESSAGE_IN_OUT.three;
  type:
    | typeof MESSAGE_TYPE.email
    | typeof MESSAGE_TYPE.fax
    | typeof MESSAGE_TYPE.meeting
    | typeof MESSAGE_TYPE.emailMeeting;
  hasReplies: boolean;
  hasAttachments: boolean;
  hasComments: boolean;
  hasMetadata: boolean;
  hasFolders: boolean;
  hasCases: boolean;
};
