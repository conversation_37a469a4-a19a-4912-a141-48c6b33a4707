import { type FC, type ReactElement } from 'react';
import { SectionList } from 'react-native';
import { MessageItem } from 'b-ui-lib';
import type { Message } from './message';
import { MessageHeader } from './MessageHeader';
import { ListEmptyComponent } from '../ListEmptyComponent';
import { MessageListSkeleton } from './MessageListSkeleton';
import Animated from 'react-native-reanimated';
import { TEST_IDS } from '../../constants/testIds';

const AnimatedSectionList = Animated.createAnimatedComponent(
  SectionList<Message>
);

type MessageSection = {
  title: string;
  data: Message[];
};

export type MessagesSectionListProps = {
  // Sections
  sections: MessageSection[];
  emptyText?: string;
  isSkeletonLoading: boolean;

  // Messages actions
  isMultiSelectActive: boolean;
  selectedMessagesIds: string[];
  handleTapMessage: (messageId: string) => void;
  handleLongTapToSelectEmail: (messageId: string) => void;
  handleTapToSelectAdditionalEmail: (messageId: string) => void;
  handleDeselectMessage: (messageId: string) => void;
  handleFlagPress?: () => void;
  loadMoreEmails: () => void;
  listFooterComponent: ReactElement;
  initialNumToRender?: number;
};

export const MessageList: FC<MessagesSectionListProps> = ({
  sections,
  emptyText,
  isSkeletonLoading,
  isMultiSelectActive,
  selectedMessagesIds,
  handleTapMessage,
  handleLongTapToSelectEmail,
  handleTapToSelectAdditionalEmail,
  handleDeselectMessage,
  handleFlagPress,
  loadMoreEmails,
  listFooterComponent,
  initialNumToRender,
  ...restProps
}) => {
  if (isSkeletonLoading) {
    return <MessageListSkeleton />;
  }

  return (
    <AnimatedSectionList
      {...restProps}
      sections={sections}
      renderItem={({ item, index }) => (
        <MessageItem
          testID={`${TEST_IDS.messageListItem}-${index}`}
          messageSectionIndex={index}
          message={item}
          isMultiSelectActive={isMultiSelectActive}
          isChecked={
            !!selectedMessagesIds?.find(
              (selectedMessageId) => selectedMessageId === item.id
            )
          }
          handleTapMessage={handleTapMessage}
          handleLongTapToSelectEmail={handleLongTapToSelectEmail}
          handleTapToSelectAdditionalEmail={handleTapToSelectAdditionalEmail}
          handleDeselectMessage={handleDeselectMessage}
          handleFlagPress={handleFlagPress}
        />
      )}
      renderSectionHeader={({ section: { title } }) => (
        <MessageHeader title={title} />
      )}
      ListEmptyComponent={<ListEmptyComponent emptyText={emptyText} />}
      onEndReached={loadMoreEmails}
      onEndReachedThreshold={0.9} // Trigger when 50% of the way down
      ListFooterComponent={listFooterComponent}
      initialNumToRender={initialNumToRender} // Initially render 100 items
    />
  );
};
