import { StyleSheet, View } from 'react-native';
import { useThemeAwareObject } from '../theme/useThemeAwareObjects';
import type { Theme } from '../constants/theme/ThemeInterface';

// Components
import { IconTextButton } from './IconTextButton';

type Props = {
  iconName: string;
  focused: boolean;
  count?: string;
  onPress: () => void;
  testID?: string;
};

export const TabBarLabel = ({
  iconName,
  focused,
  count,
  onPress,
  testID,
}: Props) => {
  const { color, styles } = useThemeAwareObject(createStyles);

  return (
    <View testID={testID} style={styles.container}>
      <IconTextButton
        iconName={iconName}
        iconSize={24}
        iconColor={
          focused ? `${color.MESSAGE_FLAG}` : `${color.TEXT_DISABLED_2}`
        }
        textStyle={{
          color: focused ? color.MESSAGE_FLAG : color.TEXT_DISABLED_2,
        }}
        title={count}
        onPress={onPress}
      />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      justifyContent: 'center',
    },
  });

  return { styles, color };
};
