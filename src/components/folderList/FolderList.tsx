import { type FC } from 'react';
import {
  type ViewStyle,
  type ScrollViewProps,
  StyleSheet,
  ScrollView,
  View,
} from 'react-native';
import { type Theme, useThemeAwareObject, CustomText } from 'b-ui-lib';
import { FolderItem } from './FolderItem';
import type { Folder } from './folder';

export type FolderListProps = ScrollViewProps & {
  folders?: Folder[];
  selectedFolderId?: string;
  handleFolderPress?: (folderId: string) => void;
  handleFolderPressSearchItem?: (folderId: string) => void;
  handleFolderExpand: (folderId: string) => void;
  emptyFoldersTitle?: string;
  containerStyle?: ViewStyle;
  testID?: string;
  folderItemTestIDPrefix?: string;
  emptyStateTestID?: string;
};

export const FolderList: FC<FolderListProps> = ({
  folders,
  selectedFolderId,
  handleFolderPress,
  handleFolderPressSearchItem,
  handleFolderExpand,
  emptyFoldersTitle,
  containerStyle,
  testID,
  folderItemTestIDPrefix,
  emptyStateTestID,
  ...restProps
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  if (!folders || !folders?.length) {
    return (
      <View testID={emptyStateTestID}>
        <CustomText>{emptyFoldersTitle ?? 'No folders were found!'}</CustomText>
      </View>
    );
  }

  return (
    <ScrollView
      {...restProps}
      testID={testID}
      style={[styles.container, containerStyle]}
    >
      {folders.map((folder, index) => (
        <FolderItem
          key={folder.id}
          folder={folder}
          selectedFolderId={selectedFolderId}
          handleFolderPress={handleFolderPress && handleFolderPress}
          handleFolderPressSearchItem={
            handleFolderPressSearchItem && handleFolderPressSearchItem
          }
          handleFolderExpand={handleFolderExpand}
          testID={
            folderItemTestIDPrefix
              ? `${folderItemTestIDPrefix}-${index}`
              : undefined
          }
        />
      ))}
    </ScrollView>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MODAL_BACKGROUND_COLOR,
    },
  });

  return { styles, color };
};
