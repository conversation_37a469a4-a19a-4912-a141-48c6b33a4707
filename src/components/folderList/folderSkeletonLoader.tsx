import { type FC } from 'react';
import { StyleSheet, View } from 'react-native';
import { type Theme, useThemeAwareObject } from 'b-ui-lib';
import ContentLoader, { Rect } from 'react-content-loader/native';

const LEVEL_PADDING = 15;

export type Props = {
  isVisible?: boolean;
  numberOfItems?: number;
  level?: number;
  testID?: string;
};

export const FolderSkeletonLoader: FC<Props> = ({
  isVisible = false,
  numberOfItems = 2,
  level = 1,
  testID,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (!isVisible) return null;

  return (
    <View testID={testID} style={styles.container}>
      {[...Array(numberOfItems)].map((_, index) => (
        <ContentLoader
          key={index}
          width="100%"
          height={50}
          speed={1}
          backgroundColor={color.SKELETON_BACKGROUND}
          foregroundColor={color.SKELETON_FOREGROUND}
        >
          {/* folder icon */}
          <Rect
            x={44 + (level - 1) * LEVEL_PADDING}
            y="20"
            rx="4"
            ry="4"
            width="15"
            height="15"
          />

          {/* name */}
          <Rect
            // TODO Fix nested level padding
            x={70 + (level - 1) * LEVEL_PADDING}
            y="22"
            rx="4"
            ry="4"
            width="60"
            height="12"
          />

          {/* expand icon */}
          <Rect x="390" y="20" rx="4" ry="4" width="15" height="15" />
        </ContentLoader>
      ))}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MODAL_BACKGROUND_COLOR,
    },
  });

  return { styles, color };
};
