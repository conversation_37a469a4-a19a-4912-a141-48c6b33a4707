import { type FC } from 'react';
import { Pressable, StyleSheet, View } from 'react-native';
import {
  BenefitIconSet,
  CustomText,
  type Folder,
  FONT_SIZES,
  FONT_WEIGHTS,
  IconButton,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import { FolderSkeletonLoader } from './folderSkeletonLoader';

const MAX_LEVEL = 4;

type Props = {
  folder?: Folder;
  selectedFolderId?: string;
  handleFolderPress?: (folderId: string) => void;
  handleFolderPressSearchItem?: (folderId: string) => void;
  handleFolderExpand: (folderId: string) => void;
  level?: number;
  testID?: string;
  headerTestID?: string;
  folderIconTestID?: string;
  nameTestID?: string;
  countTestID?: string;
  expandButtonTestID?: string;
  skeletonLoaderTestID?: string;
  childTestIDPrefix?: string;
};

export const FolderItem: FC<Props> = ({
  folder,
  selectedFolderId,
  handleFolderPress,
  handleFolderPressSearchItem,
  handleFolderExpand,
  level = 0,
  testID,
  headerTestID,
  folderIconTestID,
  nameTestID,
  countTestID,
  expandButtonTestID,
  skeletonLoaderTestID,
  childTestIDPrefix,
}) => {
  const {
    id,
    name,
    isExpanded,
    isSearchItem,
    parentId,
    emailsCount,
    hasChildren,
    isLoading,
    children,
  } = folder || {};

  const isSelected = selectedFolderId === id;
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, level, hasChildren, isSelected)
  );

  const handlePressFolder = () => {
    if (isSearchItem) {
      return (
        handleFolderPressSearchItem && handleFolderPressSearchItem(id || '')
      );
    }

    return handleFolderPress && handleFolderPress(id || '');
  };

  const increasedLevel = level < MAX_LEVEL ? level + 1 : MAX_LEVEL;

  return (
    <View testID={testID} style={styles.container}>
      <View>
        <Pressable
          testID={headerTestID}
          onPress={handlePressFolder}
          style={styles.header}
        >
          <View style={styles.headerTextContainer}>
            {!hasChildren && parentId && !isSearchItem && (
              <BenefitIconSet
                testID={folderIconTestID}
                name={'folder'}
                size={16}
                color={color.HALF_DIMMED}
              />
            )}

            {!hasChildren && parentId && isSearchItem && (
              <BenefitIconSet
                name={'search'}
                size={16}
                color={color.HALF_DIMMED}
              />
            )}

            <View style={styles.textNameContainer}>
              <CustomText testID={nameTestID} style={styles.headerTextName}>
                {name}
              </CustomText>

              {emailsCount !== null &&
                emailsCount !== undefined &&
                emailsCount !== '' &&
                emailsCount?.toString() !== '0' && (
                  <CustomText
                    testID={countTestID}
                    style={styles.headerTextCount}
                  >
                    {`(${emailsCount?.toString()})`}
                  </CustomText>
                )}
            </View>
          </View>

          {hasChildren && (
            <IconButton
              testID={expandButtonTestID}
              name={isExpanded ? 'chevron-down' : 'chevron-up'}
              size={16}
              color={color.TEXT_DEFAULT}
              onPress={() => {
                handleFolderExpand(id || '');
              }}
              hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
            />
          )}
        </Pressable>

        <View style={styles.border} />
      </View>

      <FolderSkeletonLoader
        testID={skeletonLoaderTestID}
        isVisible={isLoading}
        level={increasedLevel}
      />

      {!isLoading && children && children?.length > 0 && isExpanded && (
        <View>
          {children?.map((child, index) => (
            <FolderItem
              key={child.id}
              folder={child}
              selectedFolderId={selectedFolderId}
              handleFolderPress={handleFolderPress}
              handleFolderPressSearchItem={handleFolderPressSearchItem}
              handleFolderExpand={handleFolderExpand}
              level={increasedLevel}
              testID={
                childTestIDPrefix ? `${childTestIDPrefix}-${index}` : undefined
              }
            />
          ))}
        </View>
      )}
    </View>
  );
};

const createStyles = (
  { color }: Theme,
  level: number = 0,
  hasChildren?: boolean,
  isSelected?: boolean
) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.PRESSABLE,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      backgroundColor: isSelected
        ? color.MESSAGE_ITEM__SELECTED_BACKGROUND
        : color.MODAL_BACKGROUND_COLOR,
      paddingVertical: SPACING.EIGHTEEN,
      paddingRight: SPACING.TWENTY_TWO,
      paddingLeft:
        level === 0 ? SPACING.TWENTY_TWO : level * 20 + SPACING.TWENTY_TWO,
    },
    headerTextContainer: {
      alignItems: 'center',
      flexDirection: 'row',
      gap: 10,
    },
    textNameContainer: {
      flexDirection: 'row',
      gap: 12,
    },
    headerTextName: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight:
        hasChildren || level === 0
          ? FONT_WEIGHTS.BOLD
          : FONT_WEIGHTS.FIVE_HUNDRED,
    },
    headerTextCount: {
      fontSize: FONT_SIZES.FOURTEEN,
    },
    border: {
      flex: 1,
      borderTopWidth: 1,
      borderTopColor: color.BORDER_EMAIL_FOLDER,
    },
  });

  return { styles, color };
};
