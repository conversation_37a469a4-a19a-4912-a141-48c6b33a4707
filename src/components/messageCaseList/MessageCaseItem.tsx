import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  type MessageCase,
  CustomText,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
  BenefitIconSet,
} from 'b-ui-lib';
import { formatDate } from '../../helpers/formatDate';

type Props = {
  caseItem: MessageCase;
  containerStyle?: ViewStyle;
};

export const MessageCaseItem: FC<Props> = ({ caseItem, containerStyle }) => {
  const { name, date } = caseItem || {};

  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.containerBody}>
        <View style={styles.briefcaseIcon}>
          <BenefitIconSet name="briefcase" size={16} color={color.WHITE} />
        </View>

        <CustomText style={styles.name}>{name}</CustomText>

        <View style={styles.dateContainer}>
          <CustomText style={styles.dateText}>Added on</CustomText>
          <CustomText style={styles.date}> {formatDate(date)}</CustomText>
        </View>
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER_COLOR_FORM,
    },
    containerBody: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: SPACING.M,
      paddingVertical: SPACING.EIGHTEEN,
      paddingHorizontal: SPACING.TWENTY_TWO,
    },
    briefcaseIcon: {
      backgroundColor: color.ORANGE_BRIEFCASE,
      padding: SPACING.XS,
      borderRadius: SPACING.M,
    },
    name: {
      flex: 1,
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
    dateContainer: {
      justifyContent: 'flex-end',
      alignItems: 'center',
    },
    dateText: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_GREY_DARKER,
    },
    date: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_GREY,
    },
  });

  return { styles, color };
};
