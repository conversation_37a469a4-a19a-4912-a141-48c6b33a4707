import { type FC } from 'react';
import { FlatList } from 'react-native';
import { type MessageCase, MessageCaseItem } from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';

type Props = CreateOmittedFlatListProps<MessageCase> & {
  cases: MessageCase[];
};

export const MessageCaseList: FC<Props> = ({ cases, ...restProps }) => {
  return (
    <FlatList
      {...restProps}
      keyExtractor={(item) => item?.id}
      data={cases}
      renderItem={({ item }) => <MessageCaseItem caseItem={item} />}
    />
  );
};
