import { type FC } from 'react';
import { FlatList } from 'react-native';
import { type MessageFolder, MessageFolderItem } from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';

type Props = CreateOmittedFlatListProps<MessageFolder> & {
  folders: MessageFolder[];
  testID?: string;
  itemTestIDPrefix?: string;
};

export const MessageFolderList: FC<Props> = ({
  folders,
  testID,
  itemTestIDPrefix,
  ...restProps
}) => {
  return (
    <FlatList
      {...restProps}
      testID={testID}
      keyExtractor={(item) => item?.id}
      data={folders}
      renderItem={({ item, index }) => (
        <MessageFolderItem
          folder={item}
          testID={itemTestIDPrefix ? `${itemTestIDPrefix}-${index}` : undefined}
        />
      )}
    />
  );
};
