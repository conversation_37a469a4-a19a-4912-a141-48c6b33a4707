import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  type MessageFolder,
  CustomText,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
  BenefitIconSet,
} from 'b-ui-lib';

type Props = {
  folder: MessageFolder;
  containerStyle?: ViewStyle;
  testID?: string;
  iconTestID?: string;
  nameTestID?: string;
  pathTestID?: string;
};

export const MessageFolderItem: FC<Props> = ({
  folder,
  containerStyle,
  testID,
  iconTestID,
  nameTestID,
  pathTestID,
}) => {
  const { name, path, backgroundColor } = folder || {};

  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View testID={testID} style={[styles.container, containerStyle]}>
      <View style={styles.containerBody}>
        <View style={styles.nameWrapper}>
          <BenefitIconSet testID={iconTestID} name="folder" />

          <CustomText
            testID={nameTestID}
            style={[
              styles.name,
              {
                backgroundColor:
                  backgroundColor ?? color.OUTBOUND_EMAIL_ARROW_ICON,
              },
            ]}
          >
            {name}
          </CustomText>
        </View>
        <CustomText testID={pathTestID} style={styles.path}>
          {path}
        </CustomText>
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      borderTopWidth: 1,
      borderTopColor: color.BORDER_COLOR_FORM,
    },
    containerBody: {
      gap: SPACING.SIX,
      paddingTop: SPACING.M,
      paddingBottom: SPACING.S,
      alignItems: 'flex-start',
    },
    nameWrapper: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    name: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DISABLED_2,
      paddingVertical: SPACING.XXS,
      paddingHorizontal: SPACING.SIX,
      borderRadius: 4,
    },
    path: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DEFAULT,
    },
  });

  return { styles, color };
};
