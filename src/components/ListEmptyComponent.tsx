import { type FC } from 'react';
import { StyleSheet, type TextStyle, View, type ViewStyle } from 'react-native';
import {
  useThemeAwareObject,
  type Theme,
  CustomText,
  SPACING,
  FONT_SIZES,
} from 'b-ui-lib';

type Props = {
  emptyText?: string;
  style?: {
    container?: ViewStyle;
    text?: TextStyle;
  };
};

export const ListEmptyComponent: FC<Props> = ({ emptyText, style }) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, style?.container]}>
      <CustomText style={[styles.textStyle, style?.text]}>
        {emptyText ? emptyText : 'No emails were found'}
      </CustomText>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      paddingTop: SPACING.XXL,
      justifyContent: 'center',
      alignItems: 'center',
    },
    textStyle: {
      fontSize: FONT_SIZES.FOURTEEN,
    },
  });

  return { styles, color };
};
