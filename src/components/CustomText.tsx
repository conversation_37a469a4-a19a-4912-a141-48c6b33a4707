import React, { useCallback } from 'react';
import {
  type StyleProp,
  type TextStyle,
  type TextProps,
  StyleSheet,
  Text,
} from 'react-native';
import { useThemeAwareObject, type Theme } from 'b-ui-lib';
import { useClipboard } from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';

type Props = TextProps & {
  style?: StyleProp<TextStyle>;
  children?: any;
  testID?: string;
  canCopy?: boolean;
  numberOfLines?: number;
  onTextLayout?: (event: any) => void;
  onPress?: () => void;
};

export const CustomText: React.FC<Props> = ({
  style,
  children,
  testID,
  canCopy = false,
  numberOfLines,
  onTextLayout,
  onPress,
  ...restProps
}) => {
  const { styles } = useThemeAwareObject(createStyles);
  const [_, setString] = useClipboard();

  const writeToClipboard = useCallback(async () => {
    if (!canCopy) return;

    if (children) {
      Toast.show({
        type: 'info',
        text1: `Copied "${children}" to clipboard!`,
        onPress() {
          Toast.hide();
        },
      });

      setString(children);
    }
  }, [canCopy, children, setString]);

  return (
    <>
      {canCopy ? (
        <Text
          {...restProps}
          onLongPress={() => writeToClipboard()}
          testID={testID}
          style={[styles.textStyle, style]}
          ellipsizeMode="tail"
          onTextLayout={onTextLayout}
          onPress={onPress}
        >
          {children ? children : '-'}
        </Text>
      ) : (
        <Text
          {...restProps}
          testID={testID}
          style={[styles.textStyle, style]}
          numberOfLines={numberOfLines}
          ellipsizeMode="tail"
          onTextLayout={onTextLayout}
          onPress={onPress}
        >
          {children}
        </Text>
      )}
    </>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    textStyle: {
      fontFamily: 'Inter-Regular',
      color: color.TEXT_DEFAULT,
    },
  });

  return { styles, color };
};
