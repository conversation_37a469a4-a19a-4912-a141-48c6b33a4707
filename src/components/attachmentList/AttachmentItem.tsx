import React from 'react';
import { ActivityIndicator, Pressable, StyleSheet } from 'react-native';
import {
  type Attachment,
  type Theme,
  CustomText,
  IconButton,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';
import { TEST_IDS } from '../../constants/testIds';

type Props = {
  attachment: Attachment;
  handleDownload?: (attachmentId: string) => void;
  testID?: string;
};

export const AttachmentItem: React.FC<Props> = ({
  attachment,
  handleDownload,
  testID,
}) => {
  const { id, name, isLoading } = attachment || {};
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <Pressable
      testID={testID}
      style={styles.container}
      onPress={() => handleDownload && handleDownload(id)}
    >
      <CustomText style={styles.text} numberOfLines={1}>
        {name}
      </CustomText>

      {!isLoading ? (
        <IconButton
          testID={TEST_IDS.attachmentListItemDownloadButton}
          name={'download'}
          size={16}
          color={color.BRAND_BLUE}
          onPress={() => handleDownload && handleDownload(id)}
        />
      ) : (
        <ActivityIndicator size="small" color={color.BRAND_BLUE} />
      )}
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: SPACING.S,
      paddingHorizontal: SPACING.M,
      paddingVertical: SPACING.S,
      backgroundColor: color.PRESSABLE_HOVER,
      borderRadius: SPACING.XS,
    },
    text: {
      flex: 1,
    },
  });

  return { styles, color };
};
