import { type FC } from 'react';
import { FlatList, View, StyleSheet } from 'react-native';
import {
  type Attachment,
  type Theme,
  AttachmentItem,
  useThemeAwareObject,
} from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';
import { TEST_IDS } from '../../constants/testIds';

type Props = CreateOmittedFlatListProps<Attachment> & {
  attachments: Attachment[];
  handleDownload?: (attachmentId: string) => void;
};

export const AttachmentList: FC<Props> = ({
  attachments,
  handleDownload,
  ...restProps
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <FlatList
      {...restProps}
      keyExtractor={(item) => item?.id}
      data={attachments}
      renderItem={({ item, index }) => (
        <AttachmentItem
          testID={`${TEST_IDS.attachmentListItem}-${index}`}
          key={item.id}
          attachment={item}
          handleDownload={handleDownload}
        />
      )}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
    />
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    separator: {
      height: 10,
    },
  });

  return { styles, color };
};
