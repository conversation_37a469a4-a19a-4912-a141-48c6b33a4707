import React from 'react';
import {
  StyleSheet,
  Pressable,
  type TextStyle,
  type ViewStyle,
  type GestureResponderEvent,
  type PressableProps,
} from 'react-native';
import {
  CustomText,
  BenefitIconSet,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';

export const ICON_POSITIONS = {
  top: 'top',
  bottom: 'bottom',
  left: 'left',
  right: 'right',
};

const CONTAINER_FLEX_DIRECTION: Partial<
  Record<IconPosition, ViewStyle['flexDirection']>
> = {
  [ICON_POSITIONS.top]: 'column',
  [ICON_POSITIONS.bottom]: 'column-reverse',
  [ICON_POSITIONS.right]: 'row-reverse',
  [ICON_POSITIONS.left]: 'row',
};

type IconPosition =
  | typeof ICON_POSITIONS.top
  | typeof ICON_POSITIONS.bottom
  | typeof ICON_POSITIONS.left
  | typeof ICON_POSITIONS.right;

type Props = PressableProps & {
  iconPosition?: IconPosition;
  iconName: string;
  iconSize?: number;
  iconColor?: string;
  title?: string;
  onPress?: (e: GestureResponderEvent) => void;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
  testID?: string;
  isDisabled?: boolean;
};

export const IconTextButton: React.FC<Props> = ({
  iconPosition,
  iconName,
  iconSize,
  iconColor,
  title,
  onPress,
  containerStyle,
  textStyle,
  testID,
  isDisabled,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, iconPosition, isDisabled)
  );

  return (
    <Pressable
      {...restProps}
      testID={testID}
      style={[styles.container, containerStyle]}
      onPress={onPress}
    >
      {!!iconName && (
        <BenefitIconSet
          name={iconName}
          size={iconSize ?? 24}
          color={iconColor ?? color.TEXT_DEFAULT}
        />
      )}

      {!!title && (
        <CustomText style={[styles.title, textStyle]}>{title}</CustomText>
      )}
    </Pressable>
  );
};

const createStyles = (
  { color }: Theme,
  iconPosition?: IconPosition,
  isDisabled?: boolean
) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection:
        iconPosition && CONTAINER_FLEX_DIRECTION[iconPosition]
          ? CONTAINER_FLEX_DIRECTION[iconPosition]
          : 'row',
      justifyContent: 'center',
      alignItems: 'center',
      gap: SPACING.XS,
      opacity: isDisabled ? 0.5 : 1,
    },
    title: {
      color: color.TEXT_DEFAULT,
      textAlign: 'center',
      fontSize: 10,
    },
  });

  return { styles, color };
};
