import { type FC, useState } from 'react';
import {
  ScrollView,
  StyleSheet,
  useWindowDimensions,
  View,
  type ViewStyle,
} from 'react-native';
import {
  type MessageComment,
  type Theme,
  Avatar,
  CustomText,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
  IconButton,
  IconTextButton,
  ICON_POSITIONS,
  CommentRecipientsListModal,
  CommentAttachmentListModal,
  type Attachment,
  type AvatarEmail,
} from 'b-ui-lib';
import { formatDate } from '../../helpers/formatDate';
import RenderHtml, {
  defaultHTMLElementModels,
  HTMLElementModel,
} from 'react-native-render-html';

export type CommentItemProps = {
  comment: MessageComment;
  isExpanded?: boolean;
  handlePressExpand?: (commentId: string) => void;
  handlePressStar: (commentId: string) => void;
  handlePressReply: (commentId: string) => void;
  isNested?: boolean;
  attachmentsListModalTitle?: string;
  recipientsListModalTitle?: string;
  handleDownloadAttachment?: (attachmentsIds: string) => void;
  handleDownloadAllAttachments: (commentIds: string[]) => void;
  containerStyle?: ViewStyle;
  attachments: { byId: { [id: string]: Attachment[] }; allIds: string[] };
  fetchNotifiedUsers: (id: string) => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string | null;
  participantsList: AvatarEmail[];
};

export const MessageCommentItem: FC<CommentItemProps> = ({
  comment,
  isExpanded,
  handlePressExpand,
  handlePressStar,
  handlePressReply,
  isNested,
  attachmentsListModalTitle,
  recipientsListModalTitle,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  attachments,
  containerStyle,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  participantsList,
}) => {
  const {
    id,
    avatarName,
    username,
    date,
    description,
    isStarred,
    attachmentIds,
    notifiedUsers,
    replies,
  } = comment || {};
  const [isAttachmentsListModalOpen, setIsAttachmentsListModalOpen] =
    useState<boolean>(false);
  const [isRecipientsListModalOpen, setIsRecipientsListModalOpen] =
    useState<boolean>(false);

  const { width } = useWindowDimensions();

  const { styles, color } = useThemeAwareObject((theme) =>
    createStyles(theme, isNested)
  );

  const attachmentsCount = attachmentIds?.length
    ? attachmentIds?.length?.toString()
    : '0';

  const openAttachmentsListModal = () => {
    setIsAttachmentsListModalOpen(true);
  };

  const closeAttachmentsListModal = () => setIsAttachmentsListModalOpen(false);

  const openRecipientsListModal = () => {
    setIsRecipientsListModalOpen(true);
    fetchNotifiedUsers(id);
  };
  const closeRecipientsListModal = () => setIsRecipientsListModalOpen(false);

  const customHTMLElementModels = {
    ...defaultHTMLElementModels,
    fieldset: HTMLElementModel.fromCustomModel({
      tagName: 'fieldset',
      contentModel: defaultHTMLElementModels.div.contentModel,
    }),
  };

  const _downloadAllAttachments = () => {
    handleDownloadAllAttachments(attachmentIds);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.containerBody}>
        <View>
          <Avatar name={avatarName} backgroundColor={color.AVATAR_BACKGROUND} />
        </View>

        <View style={styles.commentBody}>
          <View style={styles.usernameRow}>
            <CustomText style={styles.usernameText}>{username}</CustomText>

            <IconTextButton
              iconPosition={ICON_POSITIONS.right}
              iconName="user"
              iconSize={16}
              onPress={openRecipientsListModal}
              containerStyle={styles.userCountContainer}
              textStyle={styles.userCountText}
            />

            <CommentRecipientsListModal
              isVisible={isRecipientsListModalOpen}
              handleClose={closeRecipientsListModal}
              title={recipientsListModalTitle}
              notifiedUsers={
                notifiedUsers && Array.isArray(notifiedUsers)
                  ? participantsList?.filter((participant) =>
                      notifiedUsers.some((userId) => participant.id === userId)
                    )
                  : []
              }
              fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
              fetchNotifiedUsersError={fetchNotifiedUsersError}
            />
          </View>

          <CustomText style={styles.dateText}>{formatDate(date)}</CustomText>

          <ScrollView>
            <RenderHtml
              contentWidth={width}
              source={{ html: description ? description : '' }}
              enableExperimentalMarginCollapsing
              customHTMLElementModels={customHTMLElementModels}
            />
          </ScrollView>

          {attachmentsCount && attachmentsCount !== '0' && (
            <IconTextButton
              iconName="paperclip"
              iconSize={16}
              title={attachmentsCount}
              onPress={openAttachmentsListModal}
              containerStyle={styles.paperclipContainer}
              textStyle={styles.paperClipText}
            />
          )}

          <CommentAttachmentListModal
            isVisible={isAttachmentsListModalOpen}
            handleClose={closeAttachmentsListModal}
            title={attachmentsListModalTitle}
            attachments={attachmentIds
              ?.map((attachmentId) => attachments.byId[attachmentId])
              .flat()
              .filter(
                (attachment): attachment is Attachment =>
                  attachment !== undefined
              )}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={_downloadAllAttachments}
          />
        </View>

        <View style={styles.leftColumn}>
          <IconButton
            name="star"
            size={24}
            color={isStarred ? color.PIN_ACTIVE : color.HALF_DIMMED}
            onPress={() => handlePressStar(id)}
            containerStyle={styles.starIcon}
          />

          {!isNested &&
            replies?.length !== undefined &&
            replies?.length !== null &&
            replies?.length > 0 && (
              <View style={styles.arrowContainer}>
                <CustomText style={styles.arrowText}>
                  ({replies?.length})
                </CustomText>
                <IconButton
                  name={isExpanded ? 'chevron-up' : 'chevron-down'}
                  size={22}
                  onPress={() => handlePressExpand && handlePressExpand(id)}
                />
              </View>
            )}

          {!isNested && (
            <IconButton
              name="arrow-undo-outline"
              size={22}
              color={color.MESSAGE_FLAG}
              onPress={() => handlePressReply(id)}
              containerStyle={styles.arrowUndoIcon}
            />
          )}
        </View>
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme, isNested?: boolean) => {
  const styles = StyleSheet.create({
    container: {
      borderTopWidth: 1,
      borderTopColor: color.BORDER_COLOR_FORM,
    },
    containerBody: {
      marginLeft: isNested ? 50 : 0,
      flexDirection: 'row',
      gap: SPACING.M,
      paddingTop: SPACING.M,
      paddingBottom: SPACING.SIX,
    },
    commentBody: {
      flex: 1,
      gap: 4,
    },
    usernameRow: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    usernameText: {
      fontWeight: '700',
      fontSize: FONT_SIZES.FOURTEEN,
      alignSelf: 'center',
      paddingVertical: SPACING.SIX,
    },
    userCountContainer: {
      marginLeft: 'auto',
    },
    userCountText: {
      fontSize: 12,
      color: color.TEXT_DIMMED,
    },
    dateText: {
      fontSize: FONT_SIZES.TEN,
      lineHeight: FONT_SIZES.TEN,
      color: color.TEXT_GREY,
    },
    paperclipContainer: {
      justifyContent: 'flex-start',
      paddingVertical: SPACING.SIX,
    },
    paperClipText: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DIMMED,
    },
    leftColumn: {
      gap: SPACING.TEN,
    },
    starIcon: {
      alignSelf: 'flex-end',
    },
    arrowContainer: {
      flexDirection: 'row',
      gap: 5,
      alignItems: 'center',
    },
    arrowText: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DISABLED,
    },
    arrowUndoIcon: {
      alignSelf: 'flex-end',
      marginTop: 'auto',
      paddingVertical: SPACING.XXS,
    },
  });

  return { styles, color };
};
