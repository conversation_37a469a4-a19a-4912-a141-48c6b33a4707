import { type FC, useState } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Attachment,
  type MessageComment,
  MessageCommentItem,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';

export type CommentThreadProps = {
  comment: MessageComment;
  handlePressStar: (commentId: string) => void;
  handlePressReply: (commentId: string) => void;
  attachmentsListModalTitle?: string;
  recipientsListModalTitle?: string;
  handleDownloadAttachment?: (attachmentsIds: string) => void;
  handleDownloadAllAttachments: (commentIds: string[]) => void;
  containerStyle?: ViewStyle;
  attachments: { byId: { [id: string]: Attachment[] }; allIds: string[] };
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string | null;
  participantsList: [];
};

export const MessageCommentThread: FC<CommentThreadProps> = ({
  comment,
  handlePressStar,
  handlePressReply,
  attachmentsListModalTitle,
  recipientsListModalTitle,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  attachments,
  containerStyle,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  participantsList,
}) => {
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  const { styles } = useThemeAwareObject(createStyles);

  const handlePressExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <MessageCommentItem
        comment={comment}
        isExpanded={isExpanded}
        handlePressStar={handlePressStar}
        handlePressExpand={handlePressExpand}
        handlePressReply={handlePressReply}
        isNested={false}
        attachments={attachments}
        attachmentsListModalTitle={attachmentsListModalTitle}
        recipientsListModalTitle={recipientsListModalTitle}
        handleDownloadAttachment={handleDownloadAttachment}
        handleDownloadAllAttachments={handleDownloadAllAttachments}
        fetchNotifiedUsers={fetchNotifiedUsers}
        fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
        fetchNotifiedUsersError={fetchNotifiedUsersError}
        participantsList={participantsList}
      />

      {isExpanded &&
        comment?.replies?.map((commentReply) => (
          <MessageCommentItem
            key={commentReply.id}
            comment={commentReply}
            isExpanded={isExpanded}
            handlePressStar={handlePressStar}
            handlePressReply={handlePressReply}
            attachments={attachments}
            isNested
            attachmentsListModalTitle={attachmentsListModalTitle}
            recipientsListModalTitle={recipientsListModalTitle}
            handleDownloadAttachment={handleDownloadAttachment}
            handleDownloadAllAttachments={handleDownloadAllAttachments}
            fetchNotifiedUsers={fetchNotifiedUsers}
            fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
            fetchNotifiedUsersError={fetchNotifiedUsersError}
            participantsList={participantsList}
          />
        ))}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {},
  });

  return { styles, color };
};
