import { type FC } from 'react';
import { FlatList } from 'react-native';
import {
  type Attachment,
  type MessageComment,
  MessageCommentThread,
} from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../helpers/omitFlatListProps';

type Props = CreateOmittedFlatListProps<MessageComment> & {
  comments: MessageComment[];
  handlePressStar: (commentId: string) => void;
  handlePressReply: (commentId: string) => void;
  attachmentsListModalTitle?: string;
  recipientsListModalTitle?: string;
  handleDownloadAttachment?: (attachmentsId: string) => void;
  handleDownloadAllAttachments: (commentIds: string[]) => void;
  attachments: { byId: { [id: string]: Attachment[] }; allIds: string[] };
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string | null;
  participantsList: [];
};

export const MessageCommentList: FC<Props> = ({
  comments,
  handlePressStar,
  handlePressReply,
  attachmentsListModalTitle,
  recipientsListModalTitle,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  attachments,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  participantsList,
  ...restProps
}) => {
  return (
    <FlatList
      {...restProps}
      keyExtractor={(item) => item?.id}
      data={comments}
      renderItem={({ item }) => (
        <MessageCommentThread
          comment={item}
          attachments={attachments}
          handlePressStar={handlePressStar}
          handlePressReply={handlePressReply}
          attachmentsListModalTitle={attachmentsListModalTitle}
          recipientsListModalTitle={recipientsListModalTitle}
          handleDownloadAttachment={handleDownloadAttachment}
          handleDownloadAllAttachments={handleDownloadAllAttachments}
          fetchNotifiedUsers={fetchNotifiedUsers}
          fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
          fetchNotifiedUsersError={fetchNotifiedUsersError}
          participantsList={participantsList}
        />
      )}
    />
  );
};
