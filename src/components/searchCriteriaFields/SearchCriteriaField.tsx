import { type FC } from 'react';
import {
  type TextInputProps,
  type GestureResponderEvent,
  Pressable,
  StyleSheet,
  TextInput,
  View,
  type ViewStyle,
} from 'react-native';
import {
  type Theme,
  BenefitIconSet,
  CustomText,
  FONT_SIZES,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';

export type SearchCriteriaFieldProps = TextInputProps & {
  value: string;
  onChangeText: (text: string) => void;
  label?: string;
  placeholder?: string;
  editable?: boolean;
  iconName?: string;
  iconColor?: string;
  onPress?: (event: GestureResponderEvent) => void;
  suggestions?: string[];
  handleSuggestionPress?: (suggestionId: string) => void;
  errorText?: string;
  style?: {
    container: ViewStyle;
  };
  testID: string;
};

export const SearchCriteriaField: FC<SearchCriteriaFieldProps> = ({
  value,
  onChangeText,
  label,
  placeholder,
  editable = true,
  iconName,
  iconColor,
  onPress,
  suggestions,
  handleSuggestionPress,
  errorText,
  style,
  testID,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View>
      <Pressable
        testID={testID}
        onPress={onPress && onPress}
        style={[styles.container, style?.container]}
      >
        {label && <CustomText style={styles.labelText}>{label}</CustomText>}

        <View style={styles.inputContainer}>
          <TextInput
            {...restProps}
            style={styles.input}
            value={value}
            onChangeText={(inputValue) =>
              onChangeText && onChangeText(inputValue)
            }
            autoCapitalize="none"
            placeholder={placeholder}
            placeholderTextColor={color.SEARCH_FIELD_PLACEHOLDER}
            editable={editable}
          />

          {iconName && (
            <BenefitIconSet
              name={iconName}
              color={iconColor ? iconColor : color.BLUE_DEFAULT}
              size={16}
              style={styles.icon}
            />
          )}
        </View>

        {suggestions && suggestions?.length > 0 && (
          <Pressable style={styles.suggestionsContainer}>
            {suggestions?.map((suggestion: string, index: number) => (
              <CustomText
                testID={`${testID}-${index}-${suggestion}`}
                key={suggestion}
                style={styles.suggestionText}
                onPress={() =>
                  handleSuggestionPress && handleSuggestionPress(suggestion)
                }
              >
                {`#${suggestion}`}
              </CustomText>
            ))}
          </Pressable>
        )}
      </Pressable>

      {errorText && (
        <CustomText style={styles.errorText}>{errorText}</CustomText>
      )}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MESSAGE_BUTTONS_BACKGROUND,
      paddingTop: SPACING.XS,
      paddingRight: SPACING.XXS,
      paddingBottom: SPACING.XS,
      paddingLeft: SPACING.S,
      borderWidth: 1,
      borderColor: color.SEARCH_FIELD_BORDER,
      borderRadius: 8,
    },
    input: {
      flex: 1,
      padding: 0,
      margin: 0,
      color: color.MESSAGE_CATEGORY_ATTACHMENTS,
    },
    labelText: {
      color: color.TEXT_SEARCH_INVERTED,
    },
    inputContainer: {
      flexDirection: 'row',
      gap: SPACING.XS,
      alignItems: 'center',
    },
    icon: {
      padding: SPACING.XXS,
    },
    errorText: {
      paddingLeft: SPACING.S,
      paddingTop: SPACING.XXS,
      color: color.ERROR,
      fontSize: FONT_SIZES.TWELVE,
    },
    suggestionsContainer: {
      flexDirection: 'row',
      gap: SPACING.XXS,
      alignItems: 'center',
      flexWrap: 'wrap',
      paddingVertical: SPACING.TEN,
    },
    suggestionText: {
      borderRadius: SPACING.XXS,
      backgroundColor: color.KEYWORD_SUGGESTION_BACKGROUND_COLOR,
      color: color.KEYWORD_SUGGESTION_COLOR,
      paddingHorizontal: SPACING.SIX,
      paddingVertical: SPACING.XXS,
      fontSize: FONT_SIZES.TWELVE,
      fontWeight: 'bold',
    },
  });

  return { styles, color };
};
