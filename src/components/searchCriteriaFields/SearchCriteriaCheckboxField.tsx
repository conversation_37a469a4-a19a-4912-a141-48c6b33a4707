import { type FC } from 'react';
import { type TextInputProps, StyleSheet, View } from 'react-native';
import {
  type Theme,
  CustomText,
  SPACING,
  useThemeAwareObject,
  Checkbox,
} from 'b-ui-lib';

export type CheckboxOption = {
  name: string;
  value: string;
};

export type SearchCriteriaCheckboxFieldProps = TextInputProps & {
  value: string;
  label?: string;
  handleCheckboxPress: (checkboxOption: CheckboxOption) => void;
  checkboxOptions: CheckboxOption[];
};

export const SearchCriteriaCheckboxField: FC<
  SearchCriteriaCheckboxFieldProps
> = ({ label, handleCheckboxPress, value, checkboxOptions }) => {
  const { styles } = useThemeAwareObject((theme) =>
    createStyles(theme, checkboxOptions?.length ?? 0)
  );

  return (
    <View style={styles.container}>
      {label && <CustomText style={styles.labelText}>{label}</CustomText>}

      <View style={styles.checkboxContainer}>
        {checkboxOptions.map((option, index) => (
          <View key={`${index}-${option.name}`} style={styles.checkboxItem}>
            <Checkbox
              testID={`testID-${index}-${option.name}`}
              isChecked={value.includes(option.value)}
              onPress={() => handleCheckboxPress(option)}
            />
            <CustomText style={styles.checkboxText}>{option.name}</CustomText>
          </View>
        ))}
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme, checkboxOptionsCount: number) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MESSAGE_BUTTONS_BACKGROUND,
      padding: SPACING.S,
      borderWidth: 1,
      borderColor: color.SEARCH_FIELD_BORDER,
      borderRadius: 8,
      gap: SPACING.FIVE,
    },
    labelText: {
      color: color.TEXT_SEARCH_INVERTED,
    },
    checkboxContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent:
        checkboxOptionsCount > 2 ? 'space-between' : 'space-evenly',
    },
    checkboxItem: {
      flexDirection: 'row',
      gap: SPACING.TEN,
    },
    checkboxText: {
      color: color.SEARCH_FIELD_PLACEHOLDER,
    },
  });

  return { styles, color };
};
