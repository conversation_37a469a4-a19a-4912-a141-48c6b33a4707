import { type FC } from 'react';
import { type ViewStyle, StyleSheet, View } from 'react-native';
import {
  type Theme,
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  useThemeAwareObject,
} from 'b-ui-lib';

type Props = {
  label?: string;
  backgroundColor?: string;
  style?: {
    container: ViewStyle;
  };
};

export const StatusLabel: FC<Props> = ({ label, backgroundColor, style }) => {
  const { styles } = useThemeAwareObject((theme) =>
    createStyles(theme, backgroundColor)
  );

  return (
    <View style={[styles.container, style?.container]}>
      <View style={styles.statusContainer}>
        <View style={styles.circle} />

        <CustomText style={styles.statusText}>{label}</CustomText>
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme, backgroundColor?: string) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
    },
    statusContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: SPACING.XXS,
      paddingVertical: 2,
      paddingHorizontal: SPACING.XS,
      borderRadius: 18,
      backgroundColor: backgroundColor ? backgroundColor : color.BRAND_DEFAULT,
    },
    circle: {
      width: 8,
      height: 8,
      backgroundColor: color.WHITE,
      borderRadius: 50,
    },
    statusText: {
      color: color.WHITE,
      fontSize: FONT_SIZES.TWELVE,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
  });

  return { styles, color };
};
