import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  FONT_SIZES,
  SPACING,
} from 'b-ui-lib';

type Props = {
  count?: string;
  text?: string;
  countColor?: string;
  style?: { container?: ViewStyle };
};

export const DocumentsCount: FC<Props> = ({
  count,
  text,
  countColor,
  style,
}) => {
  const { styles } = useThemeAwareObject((theme) =>
    createStyles(theme, countColor)
  );

  return (
    <View style={[styles.container, style?.container]}>
      <CustomText style={styles.countText}>{count ? count : '0'}</CustomText>
      <CustomText style={styles.text}>{text ? text : 'Pending'}</CustomText>
    </View>
  );
};

const createStyles = ({ color }: Theme, countColor?: string) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      gap: SPACING.XXS,
      alignItems: 'center',
      backgroundColor: color.TAB_BACKGROUND,
      paddingHorizontal: SPACING.S,
      paddingVertical: SPACING.TWENTY_TWO,
    },
    countText: {
      fontSize: FONT_SIZES.THIRTY_EIGHT,
      color: countColor ? countColor : color.BRAND_DEFAULT,
    },
    text: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.HALF_DIMMED,
    },
  });

  return { styles, color };
};
