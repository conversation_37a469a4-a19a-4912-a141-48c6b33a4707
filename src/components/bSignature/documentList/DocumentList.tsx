import { type FC } from 'react';
import { FlatList, RefreshControl, StyleSheet, View } from 'react-native';
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  SPACING,
  FONT_WEIGHTS,
  FONT_SIZES,
  DocumentListItem,
  type DocumentItem,
} from 'b-ui-lib';
import type { CreateOmittedFlatListProps } from '../../../helpers/omitFlatListProps';
import ContentLoader, { Rect } from 'react-content-loader/native';

export type DocumentListProps = CreateOmittedFlatListProps<Document> & {
  documents?: DocumentItem[];
  handlePressDocument?: (documentId: string) => void;
  isLoading?: boolean;
  errorMessage?: string;
  handleRefreshList?: () => void;
  listEmptyMessage?: string;
  skeletonItemCount?: number;
  hasDateField?: boolean;
};

export const DocumentList: FC<DocumentListProps> = ({
  documents,
  handlePressDocument,
  isLoading,
  errorMessage,
  handleRefreshList,
  listEmptyMessage,
  skeletonItemCount,
  hasDateField,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (isLoading) {
    return (
      <View>
        {[...Array(skeletonItemCount ? skeletonItemCount : 3)].map(
          (_, index) => (
            <ContentLoader
              key={index}
              width="100%"
              height={hasDateField ? 75 : 57}
              speed={1}
              backgroundColor={color.SKELETON_BACKGROUND}
              foregroundColor={color.SKELETON_FOREGROUND}
              style={styles.skeleton}
            >
              {/* Icon Placeholder */}
              <Rect x="10" y="29" rx="5" ry="5" width="22" height="25" />

              {/* Document Name */}
              <Rect
                x="45"
                y={hasDateField ? '26' : '36'}
                rx="5"
                ry="5"
                width="180"
                height="13"
              />

              {/* Due Date Placeholder */}
              {hasDateField && (
                <Rect x="45" y="51" rx="5" ry="5" width="100" height="10" />
              )}

              {/* Status Badge */}
              <Rect x="265" y="32" rx="10" ry="10" width="80" height="20" />

              {/* Arrow Icon */}
              <Rect x="360" y="35" rx="5" ry="5" width="15" height="15" />
            </ContentLoader>
          )
        )}
      </View>
    );
  }

  if (errorMessage) {
    return <CustomText style={styles.errorMessage}>{errorMessage}</CustomText>;
  }

  return (
    <FlatList
      data={documents}
      refreshControl={
        <RefreshControl
          refreshing={!!isLoading}
          onRefresh={() => handleRefreshList && handleRefreshList()}
          tintColor={color.BLACK}
        />
      }
      renderItem={({ item }) => {
        return (
          <DocumentListItem
            title={item.name}
            date={item.signedDate}
            statusDescription={item.statusDescription}
            statusBackgroundColor={item.statusBackgroundColor}
            onPress={() => handlePressDocument && handlePressDocument(item.id)}
          />
        );
      }}
      ListEmptyComponent={
        <View style={styles.emptyTextContainer}>
          <CustomText style={styles.emptyText}>
            {listEmptyMessage ? listEmptyMessage : 'No Documents in progress'}
          </CustomText>
        </View>
      }
      ItemSeparatorComponent={() => <View style={{ height: SPACING.TEN }} />}
    />
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    emptyTextContainer: {
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: 30,
    },
    emptyText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
    },
    skeleton: {
      marginBottom: 16,
    },
    errorMessage: {
      alignSelf: 'center',
      paddingTop: SPACING.M,
    },
  });

  return { styles, color };
};
