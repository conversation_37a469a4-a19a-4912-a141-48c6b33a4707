import { type FC } from 'react';
import { Pressable, StyleSheet, View, type ViewStyle } from 'react-native';
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  SPACING,
  BenefitIconSet,
  StatusLabel,
  FONT_SIZES,
  FONT_WEIGHTS,
  formatDateOnly,
} from 'b-ui-lib';

type Props = {
  title?: string;
  date?: string;
  statusDescription?: string;
  statusBackgroundColor?: string;
  onPress?: () => void;
  hideArrowIcon?: boolean;
  style?: {
    container: ViewStyle;
  };
};

export const DocumentListItem: FC<Props> = ({
  title,
  date,
  statusDescription,
  statusBackgroundColor,
  onPress,
  hideArrowIcon = false,
  style,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <Pressable onPress={onPress} style={[styles.container, style?.container]}>
      <BenefitIconSet
        name="clipboard"
        size={28}
        color={color.NOTIFICATION_ICON}
      />

      <View style={styles.titleContainer}>
        <CustomText
          numberOfLines={1}
          ellipsizeMode="tail"
          style={[styles.title]}
        >
          {title}
        </CustomText>
        {date && (
          <CustomText style={styles.dateText}>{`Due Date: ${formatDateOnly(
            date
          )}`}</CustomText>
        )}
      </View>

      <View style={styles.statusContainer}>
        <StatusLabel
          label={statusDescription}
          backgroundColor={statusBackgroundColor}
        />

        {!hideArrowIcon && (
          <BenefitIconSet
            style={styles.arrowIcon}
            name="arrow-right"
            size={22}
            color={color.DOCUMENT_ARROW_ICON}
          />
        )}
      </View>
    </Pressable>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: SPACING.S,
      paddingHorizontal: SPACING.XS,
      paddingVertical: SPACING.L,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      borderRadius: 6,
    },
    titleContainer: {
      flex: 1,
      gap: SPACING.SIX,
    },
    title: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: FONT_WEIGHTS.BOLD,
      color: color.TEXT_DEFAULT,
    },
    dateText: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DIMMED,
    },
    statusContainer: {
      gap: SPACING.S,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'flex-end',
    },
    arrowIcon: {
      marginRight: SPACING.XXS,
    },
  });

  return { styles, color };
};
