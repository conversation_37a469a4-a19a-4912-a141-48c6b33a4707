import { type FC } from 'react';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import {
  AdvanceSearchCriteriaButton,
  Button,
  BUTTON_DEFAULT_VARIANTS,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';
import { TEST_IDS } from '../../constants/testIds';

type Props = {
  searchFiltersCount?: number;
  isClearAllButtonVisible?: boolean;
  handleClearAll?: () => void;
  handlePressCriteriaButton?: () => void;
  isSearchCriteriaButtonActive?: boolean;
  style?: {
    buttonsContainer?: ViewStyle;
    criteriaButtonContainer?: ViewStyle;
  };
};

export const AdvancedSearchButtons: FC<Props> = ({
  searchFiltersCount = 0,
  isClearAllButtonVisible = false,
  handleClearAll,
  handlePressCriteriaButton,
  isSearchCriteriaButtonActive = false,
  style,
}) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, style?.buttonsContainer]}>
      {isClearAllButtonVisible && (
        <Button
          testID={TEST_IDS.advancedSearchButtonsClearAll}
          containerStyle={styles.clearAllButtonContainer}
          title="Clear All"
          variant={BUTTON_DEFAULT_VARIANTS.secondary}
          onPress={handleClearAll ?? (() => {})}
        />
      )}

      <AdvanceSearchCriteriaButton
        isActive={isSearchCriteriaButtonActive}
        handlePress={handlePressCriteriaButton && handlePressCriteriaButton}
        criteriaCount={searchFiltersCount.toString()}
        style={{
          container: [
            styles.advancedSearchButton,
            style?.criteriaButtonContainer || {},
          ],
        }}
      />
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    clearAllButtonContainer: {
      paddingVertical: 0,
      paddingHorizontal: 0,
    },
    advancedSearchButton: {
      marginLeft: 'auto',
    },
  });

  return { styles, color };
};
