import { type FC } from 'react';
import { StyleSheet, type TextStyle, View, type ViewStyle } from 'react-native';
import {
  CustomText,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  type Theme,
  useThemeAwareObject,
} from 'b-ui-lib';

type Props = {
  name?: string;
  backgroundColor?: string;
  style?: {
    container?: ViewStyle;
    text?: TextStyle;
  };
  isSmall?: boolean;
};

export const Avatar: FC<Props> = ({
  name,
  backgroundColor,
  style,
  isSmall = false,
}) => {
  const { styles } = useThemeAwareObject((theme) =>
    createStyles(theme, isSmall, backgroundColor)
  );

  return (
    <View style={[styles.container, style?.container]}>
      <CustomText style={[styles.name, style?.text]}>
        {name?.toUpperCase()}
      </CustomText>
    </View>
  );
};

const createStyles = (
  { color }: Theme,
  isSmall: boolean,
  backgroundColor?: string
) => {
  const styles = StyleSheet.create({
    container: {
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: backgroundColor ? backgroundColor : '#CC7B74',
      width: isSmall ? 14 : 32,
      height: isSmall ? 14 : 32,
      borderRadius: SPACING.M,
    },
    name: {
      color: color.WHITE,
      fontSize: isSmall ? FONT_SIZES.TEN : FONT_SIZES.FOURTEEN,
      fontWeight: isSmall ? FONT_WEIGHTS.BOLD : FONT_WEIGHTS.NORMAL,
    },
  });

  return { styles, color };
};
