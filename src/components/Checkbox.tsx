import { type FC } from 'react';
import {
  type ViewStyle,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import { type Theme, useThemeAwareObject } from 'b-ui-lib';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import { type BouncyCheckboxProps } from 'react-native-bouncy-checkbox/build/dist/BouncyCheckbox.type';

export type CheckboxProps = BouncyCheckboxProps & {
  disableText?: boolean;
  size?: number;
  isChecked: boolean;
  onPress: () => void;
  hitSlop?: { top: number; bottom: number; left: number; right: number };
  style?: ViewStyle;
  innerIconStyle?: ViewStyle;
  iconStyle?: ViewStyle;
  fillColor?: string;
  unFillColor?: string;
  testID?: string;
};

export const Checkbox: FC<CheckboxProps> = ({
  disableText = true,
  size = 16,
  isChecked,
  onPress,
  hitSlop,
  style,
  innerIconStyle,
  iconStyle,
  fillColor,
  unFillColor,
  testID,
  ...restProps
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const HIT_SLOP = { top: 10, bottom: 10, left: 20, right: 20 };

  return (
    <TouchableWithoutFeedback
      testID={testID}
      hitSlop={hitSlop ? hitSlop : HIT_SLOP}
      onPress={onPress}
    >
      <BouncyCheckbox
        {...restProps}
        size={size}
        disableText={disableText}
        isChecked={isChecked}
        onPress={onPress}
        style={[styles.checkbox, style]}
        innerIconStyle={[styles.bouncyCheckboxInnerIconStyle, innerIconStyle]}
        iconStyle={[styles.bouncyCheckboxInnerIconStyle, iconStyle]}
        fillColor={
          fillColor
            ? fillColor
            : isChecked
              ? color.MESSAGE_FLAG
              : color.INVERTED_HALF_DIMMED
        }
        unFillColor={unFillColor ? unFillColor : color.PRIMARY_DIMMED}
      />
    </TouchableWithoutFeedback>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    checkbox: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    bouncyCheckboxInnerIconStyle: {
      borderRadius: 0,
    },
  });

  return { styles, color };
};
