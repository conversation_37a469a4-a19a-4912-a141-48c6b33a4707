import { StyleSheet, View, type ViewStyle } from 'react-native';
import { useThemeAwareObject } from '../theme/useThemeAwareObjects';
import { SPACING } from '../constants/styles/spacing';
import { FONT_SIZES } from '../constants/styles/fontsSizes';

// Components
import { CustomText } from './CustomText';
import type { Theme } from '../constants/theme/ThemeInterface';

type Props = {
  title: string;
  count?: string;
  containerStyles?: ViewStyle;
};

export const TabTitle = ({ title, count, containerStyles }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={[styles.container, containerStyles]}>
      <CustomText style={styles.title}>{title}</CustomText>
      <CustomText style={styles.count}>{count}</CustomText>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginVertical: SPACING.SIX,
    },
    title: {
      fontSize: FONT_SIZES.FOURTEEN,
      color: color.TEXT_DEFAULT,
      fontWeight: '700',
    },
    count: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DIMMED,
    },
  });

  return { styles, color };
};
