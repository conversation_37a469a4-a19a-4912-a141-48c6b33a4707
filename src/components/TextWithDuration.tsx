import React, { useEffect } from 'react';
import { type StyleProp, StyleSheet, type TextStyle } from 'react-native';
import { useThemeAwareObject, type Theme, CustomText } from 'b-ui-lib';

export type TextWithDurationProps = {
  text?: string;
  cbWhenComponentHide?: () => void;
  isSuccess?: boolean;
  isError?: boolean;
  style?: StyleProp<TextStyle>;
  duration?: number;
};

export const TextWithDuration: React.FC<TextWithDurationProps> = ({
  text,
  cbWhenComponentHide,
  isSuccess,
  isError,
  style,
  duration = 2000,
}) => {
  const [isVisible, setIsVisible] = React.useState(false);

  const { styles } = useThemeAwareObject((theme) =>
    createStyles(theme, isSuccess, isError)
  );

  useEffect(() => {
    let timers = [];

    if (text) {
      setIsVisible(true);

      const timer = setTimeout(() => {
        cbWhenComponentHide?.();
        setIsVisible(false);
      }, duration);

      timers.push(timer); // Save the timer ID
    }

    return () => {
      timers.forEach(clearTimeout);
    };
  }, [text, cbWhenComponentHide, duration]);

  return isVisible ? (
    <CustomText style={[styles.text, style]}>{text}</CustomText>
  ) : null;
};

const createStyles = (
  { color }: Theme,
  isSuccess?: boolean,
  isError?: boolean
) => {
  const styles = StyleSheet.create({
    text: {
      color: isSuccess ? color.SUCCESS : isError ? color.ERROR : color.BLACK,
    },
  });

  return { styles, color };
};
