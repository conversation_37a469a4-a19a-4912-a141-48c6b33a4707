// testId structure: "transfer__genericErrorModal--leftContent"

export const TEST_IDS = {
  advancedSearchButtonsClearAll: 'advancedSearchButtons--clearAll',
  advancedSearchCriteriaButton: 'advancedSearchCriteriaButton',

  messageListItem: 'messageList--item',
  messageListFlagIcon: 'messageList--flagIcon',
  messageListUnFlagIcon: 'messageList--unFlagIcon',
  messageListCheckedCheckbox: 'messageList--checkedCheckbox',
  messageListUnCheckedCheckbox: 'messageList--unCheckedCheckbox',
  messageListUsernameBoldText: 'messageList--usernameBoldText',
  messageListUsernameNotBoldText: 'messageList--usernameNotBoldText',
  messageListSubjectText: 'messageList--subjectText',
  messageListBodyText: 'messageList--bodyText',
  messageListBottomIconsHasReplies: 'messageList__bottomIcons--hasReplies',
  messageListBottomIconsHasComments: 'messageList__bottomIcons--hasComments',
  messageListBottomIconsHasAttachments:
    'messageList__bottomIcons--hasAttachments',
  messageListBottomIconsHasFolders: 'messageList__bottomIcons--hasFolders',
  messageListBottomIconsHasMetadata: 'messageList__bottomIcons--hasMetadata',
  messageListBottomIconsHasCases: 'messageList__bottomIcons--hasCases',

  attachmentListItem: 'attachmentList--item',
  attachmentListItemDownloadButton: 'attachmentListItem--downloadButton',

  // Folder related testIds
  folderList: 'folderList',
  folderItem: 'folderItem',
  folderItemHeader: 'folderItem--header',
  folderItemName: 'folderItem--name',
  folderItemCount: 'folderItem--count',
  folderItemExpandButton: 'folderItem--expandButton',
  folderItemCheckIcon: 'folderItem--checkIcon',
  folderItemFolderIcon: 'folderItem--folderIcon',
  folderSkeletonLoader: 'folderSkeletonLoader',
  folderEmptyState: 'folderList--emptyState',

  // Message folder related testIds
  messageFolderList: 'messageFolderList',
  messageFolderItem: 'messageFolderItem',
  messageFolderItemName: 'messageFolderItem--name',
  messageFolderItemPath: 'messageFolderItem--path',
  messageFolderItemIcon: 'messageFolderItem--icon',

  messageSubject: 'message--subject',
  messageTime: 'message--time',
  messageDate: 'message--date',
};
