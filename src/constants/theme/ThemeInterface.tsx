import type { ImageProps } from 'react-native';

export type ColorTheme = {
  WHITE: string;
  BLACK: string;
  PRIMARY_DIMMED: string;
  GREY1: string;
  GREY2: string;
  GREY3: string;
  GREY_OPACITY: string;
  TEXT_DEFAULT: string;
  HALF_DIMMED: string;
  TEXT_DIMMED: string;
  TEXT_DISABLED: string;
  BLACK_TRANS: string;
  HEADER_COLOR: string;
  BRAND_BLUE: string;
  RANK_BACKGROUND: string;
  GREY6: string;
  BLUE_SEA: string;
  COMPANY_STAT_TAB: string;
  BROWN: string;
  PINK: string;
  RED: string;
  PIN_ACTIVE: string;
  BRAND_DEFAULT: string;
  ORANGE_BG: string;
  COMPANY_BACKGROUND: string;
  BACKGROUND: string;
  LIGHTGRAY: string;
  PRESSABLE: string;
  CHIP_GRAY: string;
  BORDER_COLOR: string;
  BORDER_COLOR_FORM: string;
  ORANGE: string;
  BACKDROP_COLOR: string;
  PRESSABLE_HOVER: string;
  CHIP_GREEN: string;
  TAB_BACKGROUND: string;
  TAB_BACKGROUND_ACTIVE: string;
  ERROR: string;
  DESTRUCTIVE_DEFAULT: string;
  INVERTED_DIMMED: string;
  PRIMARY_HOVER: string;
  INVERTED_HALF_DIMMED: string;
  SUCCESS: string;
  HIGHLIGHT: string;
  GEOMETRY: string;
  LABELS_TEXT_FILL: string;
  LABELS_TEXT_STROKE: string;
  ADMINISTRATIVE_GEOMETRY: string;
  ADMINISTRATIVE_COUNTRY_TEXT_FILL: string;
  ADMINISTRATIVE_LOCALITY_TEXT_FILL: string;
  POI_TEXT_FILL: string;
  POI_PARK_GEOMETRY: string;
  POI_PARK_TEXT_FILL: string;
  POI_PARK_TEXT_STROKE: string;
  GEOMETRY_FILL: string;
  ROAD_TEXT_FILL: string;
  ROAD_ARTERIAL_GEOMETRY: string;
  ROAD_HIGHWAY_GEOMETRY: string;
  ROAD_LOCAL_TEXT_FILL: string;
  TRANSIT_TEXT_FILL: string;
  WATER_GEOMETRY: string;
  WATER_TEXT_FILL: string;
  BOTTOM_TAB_ACTIVE_COLOR_BSUITE: string;
  OUTBOUND_EMAIL_ARROW_ICON: string;
  GREY_ARROW_ICON: string;
  CHECK_BLUE: string;
  EMAIL_ICON: string;
  BORDER_EMAIL_CATEGORY: string;
  TEXT_GREY: string;
  TEXT_GREY_DARKER: string;
  TEXT_GREY_MORE_DARKER: string;
  AVATAR_BACKGROUND: string;
  ARROW_EXPAND: string;
  PURPLE: string;
  ORANGE_BRIEFCASE: string;
  MESSAGE_ITEM_AVATAR_PURPLE: string;
  MESSAGE_ITEM__SUBJECT: string;
  MESSAGE_ITEM__DESC: string;
  MESSAGE_ITEM__BACKGROUND: string;
  MESSAGE_ITEM__SELECTED_BACKGROUND: string;
  MESSAGE_ITEM__ARROW_DOWN: string;
  MESSAGE_FLAG: string;
  MESSAGE_CATEGORY_ATTACHMENTS: string;
  MESSAGE_CATEGORY_ATTACHMENTS_BUTTON: string;
  MODAL_BACKGROUND_COLOR: string;
  MODAL_RECIPIENT_BACKGROUND: string;
  MODAL_RECIPIENT_TEXT: string;
  EMAIL_COLOR: string;
  BORDER_EMAIL_FOLDER: string;
  TEXT_DISABLED_2: string;
  TEXT_DEFAULT_INVETRED: string;
  MESSAGE_BUTTONS_BACKGROUND: string;
  TEXT_DEFAULT_ORANGE: string;
  TEXT_SEARCH_CRITERIA: string;
  SEARCH_CRITERIA_BORDER: string;
  SEARCH_CRITERIA_BACKGROUND_COLOR: string;
  SEARCH_CRITERIA_ICON_COLOR: string;
  BLUE_DEFAULT: string;
  SKELETON_BACKGROUND: string;
  SKELETON_FOREGROUND: string;
  TEXT_SEARCH_INVERTED: string;
  SEARCH_FIELD_BORDER: string;
  SEARCH_FIELD_PLACEHOLDER: string;
  SEARCH_FIELD_CALENDAR_ICON: string;
  KEYWORD_SUGGESTION_COLOR: string;
  KEYWORD_SUGGESTION_BACKGROUND_COLOR: string;
  MESSAGE_ITEM_ICON: string;
  NOTIFICATION_PAPERCLIP_TEXT: string;
  NOTIFICATION_FOLDER_ICON: string;
  NOTIFICATION_USERNAME: string;
  NOTIFICATION_BODY: string;
  NOTIFICATION_TITLE_BLUR: string;
  NOTIFICATION_BACK_BUTTON: string;
  NOTIFICATION_STAR_ICON: string;
  NOTIFICATION_TITLE: string;
  NOTIFICATION_ICON: string;
  NOTIFICATION_DATETIME: string;
  NOTIFICATION_BUTTON_BACKGROUND: string;
  NOTIFICATION_BUTTON_TEXT: string;
  NOTIFICATION_BOTTOM_SHEET_TEXT_COLOR: string;
  NOTIFICATION_BOTTOM_SHEET_TEXT_BACKGROUND_COLOR: string;
  NOTIFICATION_NOT_VIEWED_BODY: string;
  BOTTOM_SHEET_BACKGROUND: string;
  TASK_DETAILS_BORDER: string;
  TASK_DETAILS_USERNAME: string;
  TASK_CLOSED: string;
  TASK_NOT_ACTIVATED: string;
  DOCUMENT_COMPLETED: string;
  DOCUMENT_REQUESTED: string;
  DOCUMENT_ARROW_ICON: string;
};

export type ImageTheme = {
  CARGO_SHIP: ImageProps['source'];
  MESSAGE: ImageProps['source'];
  CALENDAR: ImageProps['source'];
  ALERT: ImageProps['source'];
  ANCHOR: ImageProps['source'];
  CHEVRON_RIGHT: ImageProps['source'];
  SEARCH: ImageProps['source'];
  USER: ImageProps['source'];
  PENCIL: ImageProps['source'];
  CHEVRON_UP: ImageProps['source'];
  ALLOTTEES: ImageProps['source'];
  FAMILY: ImageProps['source'];
  GENERAL: ImageProps['source'];
  GREEK: ImageProps['source'];
  PROPELLER: ImageProps['source'];
  CHARACTERISTICS: ImageProps['source'];
  DOCUMENTS: ImageProps['source'];
  NOTIFICATIONS: ImageProps['source'];
  UNREAD: ImageProps['source'];
  MESSAGES: ImageProps['source'];
  SERVICE_PHOTO: ImageProps['source'];
  WARNING_GREY: ImageProps['source'];
  AT_SEA: ImageProps['source'];
  DOWN: ImageProps['source'];
  UP: ImageProps['source'];
  DOWNLOAD: ImageProps['source'];
  RENEWAL: ImageProps['source'];
  INFO: ImageProps['source'];
  SAVE: ImageProps['source'];
  ARROW_RIGHT: ImageProps['source'];
  AT_PORT: ImageProps['source'];
  VESSEL_PLANS: ImageProps['source'];
  CREW_PHOTO: ImageProps['source'];
  OK: ImageProps['source'];
  ATTACHMENT: ImageProps['source'];
  CALENDAR_GREY: ImageProps['source'];
  LOCATION: ImageProps['source'];
  EYE: ImageProps['source'];
  APPRAISALS: ImageProps['source'];
  FILES: ImageProps['source'];
  MEDICAL: ImageProps['source'];
  PROFILE: ImageProps['source'];
  DOCUMENT: ImageProps['source'];
  WHEEL: ImageProps['source'];
  MAP_PIN: ImageProps['source'];
  BENEFIT_LOGO: ImageProps['source'];
  CLIPBOARD: ImageProps['source'];
  EXTERNALLINK: ImageProps['source'];
  MENUDOTS: ImageProps['source'];
  MAP_PIN_TRIP: ImageProps['source'];
  MAP_PIN_TRIP_XXXL: ImageProps['source'];
  MAP_PIN_PORT: ImageProps['source'];
  ROUTE: ImageProps['source'];
  SMALL_ARROW: ImageProps['source'];
  VESSEL_PLACEHOLDER: ImageProps['source'];
  SEAMAN_PLACEHOLDER: ImageProps['source'];
  COMMENTS: ImageProps['source'];
  ADD_COMMENT: ImageProps['source'];
  FILTER: ImageProps['source'];
  STAR: ImageProps['source'];
  ENVELOPE: ImageProps['source'];
  ENVELOPE_ACTIVE: ImageProps['source'];
  LINK: ImageProps['source'];
  USERS: ImageProps['source'];
  REPLY: ImageProps['source'];
  YELLOW_ARROW: ImageProps['source'];
  BA_LOGO: ImageProps['source'];
  AUDIO_VISUALIZATION: ImageProps['source'];
  CAMERA: ImageProps['source'];
  MICROPHONE: ImageProps['source'];
  B_AUDIT_LOGO: ImageProps['source'];
  B_IN_CHARGE_LOGO: ImageProps['source'];
  B_TEAM_LOGO: ImageProps['source'];
  B_SIGNATURE_LOGO: ImageProps['source'];
};

export type Theme = {
  id: string;
  color: ColorTheme;
  images: ImageTheme;
};
