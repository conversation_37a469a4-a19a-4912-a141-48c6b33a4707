{"compilerOptions": {"rootDir": ".", "paths": {"b-ui-lib": ["./src/index"]}, "allowUnreachableCode": false, "allowUnusedLabels": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "lib": ["ESNext"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noImplicitUseStrict": false, "noStrictGenericChecks": false, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ESNext", "verbatimModuleSyntax": true}}