# Version 0.1.32

- Add testIds in message components.
- In IconTextButton component add isDisabled prop.
- Remove check icon from FolderItem component.
- Remove email text from MessageItem and MessageInfoData components.
- Remove hash symbol in username text in MessageCommentItem component.

---

# Version 0.1.31

- Add testIds in various component.

---

# Version 0.1.30

- In searchInput component add ref prop.
- In FolderList component add skeleton loader.

---

# Version 0.1.29

- Add displayMode property in CaseList component in order to show from or assignedTo properties in CaseItem.
- Create a generic Logo component and remove BTeamLogo.
- Fix backgroundColor for messageListTab.
- Add DocumentsCount component.
- Fixes and rename for CaseStatusLabel component.
- Add DocumentList && DocumentListItem components.
- Input component style fixes.

---

# Version 0.1.28

- Minor fixes for AdvancedSearchButtons component.
- Fix email icons in MessageList.
- Create MessageListTab component.
- Create CaseList component.
- Make MessageList animated.
- Add new icons.
- Add CaseStatusLabel component.
- Add date functions helpers.

---

# Version 0.1.27

- Add AdvancedSearchButtons && AdvancedSearchCriteriaButton components.
- Add SearchCriteriaField && SearchCriteriaCheckboxField components.
- Add backdrop functionality in CustomBottomSheet.
- Add suggestion modal in SearchInput component.
- In AvatarEmailItem center username when email is not provided.
- Fixes for ListEmptyComponent.
- Add error message prop in SearchCriteriaField component.

---

# Version 0.1.26

- Add new colors && add property in Avatar component.
- Enhance attachment handling in message comments.
- Add style prop to MessageDate component.
- Add MessageInfoData, MessageInfoHeader component.
- Add MessageInfoData skeleton.
- Add CustomBottomSheet component
- Install react-navigation/native library.

---

# Version 0.1.25

- In searchInput component add suggestion dropdown and clear button.
- Add new colors.
- Handle downloading attachments from comments

---

# Version 0.1.24

- Add loader in AttachmentItem.

---

# Version 0.1.21

- Add checkbox component.
- Add rest props for some components.
- Remove gorhom/bottom-sheet from root package.json peer dependencies.

---

# Version 0.1.20

- Create new color variable pairs in themes
- Change colors to components to accomodate dark theme

---

# Version 0.1.19

- In MessageList checkbox add hitSlop to increase pressable area.
- Fix styles in MessageAvatar component.
- Remove MessageList bottomSheet logic.

---

# Version 0.1.18

- Add CommentAttachmentListModal && CommentRecipientsListModal components in MessageCommentItem.
- Fix styles in MessageCommentItem.
- Fix styles in MessageCaseMetadataList && MessageCaseList.

---

# Version 0.1.17

- Add library to render html in message comment description.
- In MessageList add scroll properties.
- Fix styles and properties in modal component.

---

# Version 0.1.16

- Fix folderItem check icon size.
- Fix styles for MessageList && MessageListItem.
- In MessageList pass actions as property and add separate component for bottomSheet.
- Add MessageListEmptyComponent for MessageList.
- Add skeleton component for MessageList.
- Separate ReadMe files.

---

# Version 0.1.15

- Fix styles for actions in MessageList bottom sheet.

---

# Version 0.1.14

- Remove horizontal padding in MessageCommentItem and MessageFolderItem components.

---

# Version 0.1.13

- Fixes for AvatarEmailList and separate AvatarEmailItem component.
- Add extra properties in Input component.
- Conditionally hide x button in SearchInput component.
- Add new colors.
- Remove unnecessary prop from AvatarEmailList component.

---

# Version 0.1.12

- Add info icon.
- Add onPress in CustomText component.
- Add AttachmentList component and type.
- Add MessageFolder component and type.
- Add MessageCaseMetadataList component and type.
- Add MessageCaseList component and type.

---

# Version 0.1.11

- Fix styles in MessageDate, MessageCommentItem, MessageReplyItem component.
- In IconTextButton remove unnecessary isActive property.
- In messageList add pull to refresh functionality.

---

# Version 0.1.10

- Fixes for MessageItem component.
- Add flag icon.
- Add Mark as unread action options functionality.

---

# Version 0.1.9

- Minor fix in MessageReplyList component.
- Fix styles in Modal component.
- Change FolderItem backgroundColor when selected.

---

# Version 0.1.8

- Add ComposeMessageInput, AvatarEmailList components and their stories.

---

# Version 0.1.7

- Add MessageReplyItem, MessageReplyList, MessageCommentItem, MessageCommentThread, MessageCaseList components and their stories.

---

# Version 0.1.6

- FolderItem fix border.
- Create mapper for button styles.
- Add new component MessageArrow.
- Add new component MessageDate.

---

# Version 0.1.5

- Add onPress on messageItem.
- Rename messageHeader component to messageAvatar.
- Add menu icon.
- Add counter text to folderItem.
- Fix nested folders margin.
- Fix styles for messageItem.
- Change implementation of messageItem bottom sheet.
- Add menu icon.

---

# Version 0.1.4

- Add messageAvatar && messageIcons components.

---

# Version 0.1.3

- Style fixes for messagesSectionList and folderList components.

---

# Version 0.1.2

- Fixes for messagesSectionList and folderList components.

---

# Version 0.1.1

- Add new component (textWithDuration).

---
