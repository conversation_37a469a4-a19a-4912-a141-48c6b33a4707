## Add new version

1. Create a new branch named for example releaseCandidates/release0116

2. Increase version in package.json

3. Enrich changelog

4. Open a PR targeting the main branch, using the new version as the title and the changelog details as the description.

### Committing changes
In order to commit changes to the project, you have to comply with the following rules:
Conventional commits: https://www.conventionalcommits.org/en/v1.0.0/

Some examples of valid commit messages are:
```
feat: add hat wobble
^--^  ^------------^
|     |
|     +-> Summary in present tense.
|
+-------> Type: chore, docs, feat, fix, refactor, style, or test.
```
