### Exporting the library
In order to export the library, you will need to run the following command from the root directory of the project:
\
```sh
npm run prepare
```
\
If the command is executed successfully, you will see a new folder in the root directory of the project named `lib`. This folder will contain the library that you can use in other projects.

In order to install it as a module first you have to pack it:
```sh
npm pack
```
This will create a tarball file in the root directory of the project. You can install it in other projects by running
```sh
npm install {pathToTheTarballFile}
```

The listed libraries in the peer dependencies should also be installed in the project that uses the library to ensure same versions.

## Usage


```js
import { BUiLibView } from "b-ui-lib";

// ...

<BUiLibView color="tomato" />
```
