# b-ui-lib

This is a UI components library designed for benefit mobile apps.
The example folder contains a separate app to test the library, including a Storybook setup.

## Installation

Run the setup.sh script which reinstalls dependencies and starts Metro. More specifically will perform the following tasks.

1. Obtain the correct node version from the .nvmrc file.
2. Delete and reinstall node_modules and iOS pods.
3. Generate stories.
4. Start Metro.

First command only needs to be run once to make the file executable.

```sh
chmod +x setup.sh
```

```sh
./setup.sh
```

---------

To start only the Metro server.

```sh
yarn example start -- --reset-cache
```

---------

And then you can launch Storybook on Android or iOS by running one of the following commands below.

```sh
yarn example android
```

```sh
yarn example ios
```

## Contributing

See the [contributing guide](CONTRIBUTING.md) to learn how to contribute to the repository and the development workflow.

## License

MIT

---

Made with [create-react-native-library](https://github.com/callstack/react-native-builder-bob)
