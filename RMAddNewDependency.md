## How to add new dependency

If our library depends on another react-native library containing native code, we should do the following:

1. Add the native library to peerDependencies of the root package.json.
2. Include the native library to devDependencies of the root package.json.
3. Add the native library to dependencies of the example package.json.

https://callstack.github.io/react-native-builder-bob/faq#how-do-i-add-a-react-native-library-containing-native-code-as-a-dependency-in-my-library

----

If our library depends on another react-native library only for development purposes then we should add the library to devDependencies of the root package.json

---

If our example app depends on another react-native library only for development purposes then we should add the library to devDependencies of the example package.json
