#ifdef RCT_NEW_ARCH_ENABLED
#import "BUiLibView.h"

#import <react/renderer/components/RNBUiLibViewSpec/ComponentDescriptors.h>
#import <react/renderer/components/RNBUiLibViewSpec/EventEmitters.h>
#import <react/renderer/components/RNBUiLibViewSpec/Props.h>
#import <react/renderer/components/RNBUiLibViewSpec/RCTComponentViewHelpers.h>

#import "RCTFabricComponentsPlugins.h"
#import "Utils.h"

using namespace facebook::react;

@interface BUiLibView () <RCTBUiLibViewViewProtocol>

@end

@implementation BUiLibView {
    UIView * _view;
}

+ (ComponentDescriptorProvider)componentDescriptorProvider
{
    return concreteComponentDescriptorProvider<BUiLibViewComponentDescriptor>();
}

- (instancetype)initWithFrame:(CGRect)frame
{
  if (self = [super initWithFrame:frame]) {
    static const auto defaultProps = std::make_shared<const BUiLibViewProps>();
    _props = defaultProps;

    _view = [[UIView alloc] init];

    self.contentView = _view;
  }

  return self;
}

- (void)updateProps:(Props::Shared const &)props oldProps:(Props::Shared const &)oldProps
{
    const auto &oldViewProps = *std::static_pointer_cast<BUiLibViewProps const>(_props);
    const auto &newViewProps = *std::static_pointer_cast<BUiLibViewProps const>(props);

    if (oldViewProps.color != newViewProps.color) {
        NSString * colorToConvert = [[NSString alloc] initWithUTF8String: newViewProps.color.c_str()];
        [_view setBackgroundColor: [Utils hexStringToColor:colorToConvert]];
    }

    [super updateProps:props oldProps:oldProps];
}

Class<RCTComponentViewProtocol> BUiLibViewCls(void)
{
    return BUiLibView.class;
}

@end
#endif
