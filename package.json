{"name": "b-ui-lib", "version": "0.1.32", "description": "react-native ui components", "source": "./src/index.tsx", "main": "./lib/commonjs/index.js", "module": "./lib/module/index.js", "exports": {".": {"import": {"types": "./lib/typescript/module/src/index.d.ts", "default": "./lib/module/index.js"}, "require": {"types": "./lib/typescript/commonjs/src/index.d.ts", "default": "./lib/commonjs/index.js"}}}, "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "react-native.config.json", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"example": "yarn workspace b-ui-lib-example", "test": "jest", "typecheck": "tsc", "lint": "eslint \"**/*.{js,ts,tsx}\"", "clean": "del-cli android/build example/android/build example/android/app/build example/ios/build lib", "prepare": "bob build", "release": "release-it"}, "keywords": ["react-native", "ios", "android"], "repository": {"type": "git", "url": "git+https://github.com/andreasglarakis/b-ui-lib.git"}, "author": "and<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/andreasglarakis)", "license": "MIT", "bugs": {"url": "https://github.com/andreasglarakis/b-ui-lib/issues"}, "homepage": "https://github.com/andreasglarakis/b-ui-lib#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@commitlint/config-conventional": "^17.0.2", "@evilmartians/lefthook": "^1.5.0", "@gorhom/bottom-sheet": "5.0.5", "@react-native-async-storage/async-storage": "2.1.0", "@react-native-clipboard/clipboard": "1.14.2", "@react-native/eslint-config": "^0.73.1", "@react-navigation/native": "6.1.6", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^29.5.5", "@types/lodash": "^4.14.178", "@types/react": "^18.2.44", "@types/react-native-vector-icons": "^6.4.18", "commitlint": "^17.0.2", "del-cli": "^5.1.0", "eslint": "^8.51.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "lodash": "^4.17.21", "prettier": "^3.0.3", "react": "18.3.1", "react-content-loader": "7.0.2", "react-native": "0.75.4", "react-native-bouncy-checkbox": "4.1.2", "react-native-builder-bob": "^0.30.2", "react-native-gesture-handler": "2.20.1", "react-native-modal": "13.0.1", "react-native-reanimated": "3.16.2", "react-native-render-html": "6.3.4", "react-native-svg": "15.9.0", "react-native-toast-message": "2.2.1", "react-native-vector-icons": "9.2.0", "release-it": "^15.0.0", "turbo": "^1.10.7", "typescript": "^5.2.2"}, "resolutions": {"@types/react": "^18.2.44"}, "peerDependencies": {"@react-native-async-storage/async-storage": "2.1.0", "@react-native-clipboard/clipboard": "1.14.2", "@react-navigation/native": "6.1.6", "@types/lodash": "^4.14.178", "lodash": "^4.17.21", "react": "*", "react-content-loader": "7.0.2", "react-native": "*", "react-native-bouncy-checkbox": "4.1.2", "react-native-gesture-handler": "2.20.1", "react-native-modal": "13.0.1", "react-native-reanimated": "3.16.2", "react-native-render-html": "6.3.4", "react-native-svg": "15.9.0", "react-native-toast-message": "2.2.1", "react-native-vector-icons": "9.2.0"}, "workspaces": ["example"], "packageManager": "yarn@3.6.1", "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "extends": ["@react-native", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["codegen", ["commonjs", {"esm": true}], ["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json", "esm": true}]]}, "codegenConfig": {"name": "RNBUiLibViewSpec", "type": "all", "jsSrcsDir": "src", "outputDir": {"ios": "ios/generated", "android": "android/generated"}, "android": {"javaPackageName": "com.builib"}, "includesGeneratedCode": true}, "create-react-native-library": {"type": "view-mixed", "version": "0.42.0"}}